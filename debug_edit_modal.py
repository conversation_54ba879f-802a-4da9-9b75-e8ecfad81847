#!/usr/bin/env python3
"""
Debug Edit Modal Issue
"""

import requests
import re

BASE_URL = "http://127.0.0.1:5000"

def debug_modal_structure():
    """Debug the modal structure in admin dashboard"""
    print("🔍 DEBUGGING EDIT MODAL STRUCTURE")
    print("=" * 50)
    
    session = requests.Session()
    
    # Login as admin
    login_data = {'username': 'admin', 'password': 'admin123'}
    login_response = session.post(f"{BASE_URL}/login", data=login_data)
    
    if login_response.status_code == 200 or login_response.status_code == 302:
        print("✅ Admin login successful")
        
        # Get admin dashboard
        dashboard_response = session.get(f"{BASE_URL}/admin/dashboard")
        html_content = dashboard_response.text
        
        # Check for edit buttons
        edit_buttons = re.findall(r'data-bs-target="#editModal(\d+)"', html_content)
        print(f"📊 Found edit buttons for lot IDs: {edit_buttons}")
        
        # Check for corresponding modals
        edit_modals = re.findall(r'id="editModal(\d+)"', html_content)
        print(f"📊 Found edit modals for lot IDs: {edit_modals}")
        
        # Check if they match
        if set(edit_buttons) == set(edit_modals):
            print("✅ Edit buttons and modals match perfectly")
        else:
            print("❌ Mismatch between buttons and modals")
            print(f"   Buttons: {set(edit_buttons)}")
            print(f"   Modals: {set(edit_modals)}")
        
        # Check Bootstrap version
        if 'bootstrap@5.3.0' in html_content:
            print("✅ Bootstrap 5.3.0 detected")
        else:
            print("⚠️ Different Bootstrap version or not found")
        
        # Check for Bootstrap JS
        if 'bootstrap.bundle.min.js' in html_content:
            print("✅ Bootstrap JavaScript bundle found")
        else:
            print("❌ Bootstrap JavaScript bundle missing")
        
        # Check for our custom JavaScript
        if 'Edit buttons initialized' in html_content:
            print("✅ Custom edit button JavaScript found")
        else:
            print("❌ Custom edit button JavaScript missing")
        
        # Check modal structure for first lot
        if edit_modals:
            first_modal_id = edit_modals[0]
            modal_pattern = f'<div class="modal fade" id="editModal{first_modal_id}"'
            if modal_pattern in html_content:
                print(f"✅ Modal structure correct for lot {first_modal_id}")
                
                # Extract the modal content for first lot
                modal_start = html_content.find(modal_pattern)
                if modal_start != -1:
                    modal_end = html_content.find('</div>', html_content.find('</form>', modal_start)) + 6
                    modal_content = html_content[modal_start:modal_end]
                    
                    # Check for required form fields
                    if 'name="location_name"' in modal_content:
                        print("✅ Location name field found")
                    if 'name="address"' in modal_content:
                        print("✅ Address field found")
                    if 'name="city"' in modal_content:
                        print("✅ City field found")
                    if 'name="state"' in modal_content:
                        print("✅ State field found")
                    if 'name="pin_code"' in modal_content:
                        print("✅ Pin code field found")
                    if 'name="price_per_hour"' in modal_content:
                        print("✅ Price field found")
                    if 'name="maximum_spots"' in modal_content:
                        print("✅ Maximum spots field found")
            else:
                print(f"❌ Modal structure incorrect for lot {first_modal_id}")
        
        return True
    else:
        print("❌ Admin login failed")
        return False

def create_modal_fix():
    """Create a more robust modal fix"""
    print("\n🔧 CREATING ENHANCED MODAL FIX")
    print("=" * 50)
    
    enhanced_js = """
// Enhanced Edit Modal Fix
document.addEventListener('DOMContentLoaded', function() {
    console.log('Initializing edit modal fix...');
    
    // Wait for Bootstrap to be fully loaded
    setTimeout(function() {
        const editButtons = document.querySelectorAll('[data-bs-toggle="modal"][data-bs-target^="#editModal"]');
        console.log('Found edit buttons:', editButtons.length);
        
        editButtons.forEach((button, index) => {
            const targetModalId = button.getAttribute('data-bs-target');
            const modal = document.querySelector(targetModalId);
            
            if (modal) {
                console.log(`Setting up button ${index + 1} for modal ${targetModalId}`);
                
                // Remove existing event listeners
                button.removeEventListener('click', handleEditClick);
                
                // Add new event listener
                button.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    
                    console.log('Edit button clicked for modal:', targetModalId);
                    
                    try {
                        // Try Bootstrap's native modal
                        const bsModal = new bootstrap.Modal(modal, {
                            backdrop: true,
                            keyboard: true,
                            focus: true
                        });
                        bsModal.show();
                        console.log('Modal shown successfully');
                    } catch (error) {
                        console.error('Error showing modal:', error);
                        // Fallback: manually show modal
                        modal.style.display = 'block';
                        modal.classList.add('show');
                        document.body.classList.add('modal-open');
                        
                        // Create backdrop
                        const backdrop = document.createElement('div');
                        backdrop.className = 'modal-backdrop fade show';
                        document.body.appendChild(backdrop);
                    }
                });
            } else {
                console.error(`Modal not found: ${targetModalId}`);
            }
        });
        
        console.log('Edit modal fix initialization complete');
    }, 500); // Wait 500ms for Bootstrap to load
});

function handleEditClick(e) {
    // Placeholder function for removing listeners
}
"""
    
    print("Enhanced JavaScript fix created:")
    print("- Adds console logging for debugging")
    print("- Waits for Bootstrap to load completely")
    print("- Includes fallback modal display method")
    print("- Prevents event bubbling issues")
    
    return enhanced_js

def main():
    print("🔍 DEBUGGING EDIT MODAL ISSUE")
    print("=" * 70)
    
    # Debug current structure
    debug_result = debug_modal_structure()
    
    if debug_result:
        # Create enhanced fix
        enhanced_js = create_modal_fix()
        
        print(f"\n📋 DIAGNOSIS:")
        print("- Modal structure appears correct")
        print("- Bootstrap 5 is properly loaded")
        print("- Issue likely related to JavaScript timing or event handling")
        
        print(f"\n💡 RECOMMENDED SOLUTION:")
        print("- Replace current JavaScript with enhanced version")
        print("- Add console logging to debug in browser")
        print("- Include fallback modal display method")
        
        print(f"\n🔧 NEXT STEPS:")
        print("1. Update the JavaScript in admin_dashboard.html")
        print("2. Test in browser with developer console open")
        print("3. Check for any JavaScript errors in console")
        print("4. Verify modal opens when edit button is clicked")
    else:
        print("\n❌ Cannot debug - admin login failed")

if __name__ == "__main__":
    main()
