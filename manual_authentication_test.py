#!/usr/bin/env python3
"""
Manual Authentication Test - Verify authentication and authorization
"""

import requests
import sys

BASE_URL = "http://127.0.0.1:5000"

def test_authentication():
    """Test authentication and authorization manually"""
    print("🔐 MANUAL AUTHENTICATION TEST")
    print("=" * 50)
    
    # Test 1: Access protected route without login
    print("\n1. Testing access to admin dashboard without login...")
    response = requests.get(f"{BASE_URL}/admin/dashboard", allow_redirects=False)
    if response.status_code == 302:
        print("   ✅ Correctly redirected (302) - Authentication working")
    else:
        print(f"   ❌ Not redirected ({response.status_code}) - Authentication issue")
    
    # Test 2: Login and access protected route
    print("\n2. Testing login and access to admin dashboard...")
    session = requests.Session()
    
    # Login as admin
    login_data = {'username': 'admin', 'password': 'admin123'}
    login_response = session.post(f"{BASE_URL}/login", data=login_data)
    print(f"   Login response: {login_response.status_code}")
    
    # Try to access admin dashboard
    dashboard_response = session.get(f"{BASE_URL}/admin/dashboard")
    if dashboard_response.status_code == 200:
        print("   ✅ Admin dashboard accessible after login")
    else:
        print(f"   ❌ Admin dashboard not accessible ({dashboard_response.status_code})")
    
    # Test 3: User trying to access admin routes
    print("\n3. Testing user access to admin routes...")
    user_session = requests.Session()
    
    # Login as regular user
    user_login_data = {'username': 'john_doe', 'password': 'password123'}
    user_login_response = user_session.post(f"{BASE_URL}/login", data=user_login_data)
    print(f"   User login response: {user_login_response.status_code}")
    
    # Try to access admin dashboard
    user_admin_response = user_session.get(f"{BASE_URL}/admin/dashboard", allow_redirects=False)
    if user_admin_response.status_code == 302:
        print("   ✅ User correctly denied access to admin dashboard (302)")
    else:
        print(f"   ❌ User not denied access ({user_admin_response.status_code})")
    
    # Test 4: Logout and access
    print("\n4. Testing logout and access...")
    session.get(f"{BASE_URL}/logout")
    
    # Try to access admin dashboard after logout
    logout_response = session.get(f"{BASE_URL}/admin/dashboard", allow_redirects=False)
    if logout_response.status_code == 302:
        print("   ✅ Correctly redirected after logout (302)")
    else:
        print(f"   ❌ Not redirected after logout ({logout_response.status_code})")
    
    # Test 5: Test forgot password functionality
    print("\n5. Testing forgot password functionality...")
    forgot_response = requests.get(f"{BASE_URL}/forgot_password")
    if forgot_response.status_code == 200:
        print("   ✅ Forgot password page accessible")
    else:
        print(f"   ❌ Forgot password page not accessible ({forgot_response.status_code})")
    
    # Test forgot password form submission
    forgot_form_response = requests.post(f"{BASE_URL}/forgot_password", data={
        'email': '<EMAIL>'
    })
    if forgot_form_response.status_code in [200, 302]:
        print("   ✅ Forgot password form submission working")
    else:
        print(f"   ❌ Forgot password form submission failed ({forgot_form_response.status_code})")

if __name__ == "__main__":
    test_authentication()
