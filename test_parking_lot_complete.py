#!/usr/bin/env python3
"""
Complete Parking Lot Functionality Test
Tests both create and edit parking lot operations
"""

import requests
import json
from datetime import datetime

BASE_URL = "http://127.0.0.1:5000"

def test_complete_parking_lot_functionality():
    """Test complete parking lot create and edit workflow"""
    print("🏢 COMPLETE PARKING LOT FUNCTIONALITY TEST")
    print("=" * 60)
    print(f"Testing at: {BASE_URL}")
    print(f"Test started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()

    # Create a session to maintain cookies
    session = requests.Session()

    # Step 1: Admin login
    print("🔐 Step 1: Admin Login")
    print("-" * 30)
    
    login_data = {
        'username': 'admin',
        'password': 'admin123'
    }
    
    try:
        login_response = session.post(f"{BASE_URL}/login", data=login_data, allow_redirects=False)
        if login_response.status_code == 302:
            print("✅ Admin login successful")
        else:
            print(f"❌ Admin login failed: {login_response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Login error: {str(e)}")
        return False

    print()

    # Step 2: Create a new parking lot
    print("🏗️ Step 2: Create New Parking Lot")
    print("-" * 30)
    
    create_data = {
        'location_name': 'COMPLETE TEST Mall',
        'address': '999 Complete Test Street',
        'city': 'Test City',
        'state': 'Test State',
        'pin_code': '999999',
        'price_per_hour': '88.88',
        'maximum_spots': '15'
    }
    
    try:
        create_response = session.post(f"{BASE_URL}/admin/create_lot", data=create_data, allow_redirects=False)
        if create_response.status_code == 302:
            print("✅ Parking lot creation successful")
            print(f"   Name: {create_data['location_name']}")
            print(f"   City: {create_data['city']}, {create_data['state']}")
            print(f"   Price: ₹{create_data['price_per_hour']}/hour")
            print(f"   Spots: {create_data['maximum_spots']}")
        else:
            print(f"❌ Parking lot creation failed: {create_response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Creation error: {str(e)}")
        return False

    print()

    # Step 3: Find the created parking lot
    print("🔍 Step 3: Find Created Parking Lot")
    print("-" * 30)
    
    try:
        api_response = session.get(f"{BASE_URL}/api/parking_lots")
        if api_response.status_code == 200:
            data = api_response.json()
            parking_lots = data.get('parking_lots', [])
            
            # Find our created lot
            created_lot = None
            for lot in parking_lots:
                if lot['location_name'] == create_data['location_name']:
                    created_lot = lot
                    break
            
            if created_lot:
                print("✅ Created parking lot found")
                print(f"   ID: {created_lot['id']}")
                print(f"   Name: {created_lot['location_name']}")
                print(f"   City: {created_lot['city']}")
                print(f"   State: {created_lot['state']}")
                print(f"   Available Spots: {created_lot['available_spots']}")
                lot_id = created_lot['id']
            else:
                print("❌ Created parking lot not found")
                return False
        else:
            print(f"❌ API request failed: {api_response.status_code}")
            return False
    except Exception as e:
        print(f"❌ API error: {str(e)}")
        return False

    print()

    # Step 4: Edit the parking lot
    print("✏️ Step 4: Edit Parking Lot")
    print("-" * 30)
    
    edit_data = {
        'location_name': 'EDITED Complete Test Mall',
        'address': '777 Edited Complete Test Avenue',
        'city': 'Edited City',
        'state': 'Edited State',
        'pin_code': '777777',
        'price_per_hour': '77.77',
        'maximum_spots': '20'  # Increase spots
    }
    
    try:
        edit_response = session.post(f"{BASE_URL}/admin/edit_lot/{lot_id}", data=edit_data, allow_redirects=False)
        if edit_response.status_code == 302:
            print("✅ Parking lot edit successful")
            print(f"   Updated Name: {edit_data['location_name']}")
            print(f"   Updated City: {edit_data['city']}, {edit_data['state']}")
            print(f"   Updated Price: ₹{edit_data['price_per_hour']}/hour")
            print(f"   Updated Spots: {edit_data['maximum_spots']}")
        else:
            print(f"❌ Parking lot edit failed: {edit_response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Edit error: {str(e)}")
        return False

    print()

    # Step 5: Verify the edit
    print("🔍 Step 5: Verify Edit Changes")
    print("-" * 30)
    
    try:
        # Wait a moment for database to update
        import time
        time.sleep(1)
        
        verify_response = session.get(f"{BASE_URL}/api/parking_lots/{lot_id}")
        if verify_response.status_code == 200:
            edited_lot = verify_response.json()
            
            print("📊 Edited parking lot details:")
            print(f"   ID: {edited_lot['id']}")
            print(f"   Name: {edited_lot['location_name']}")
            print(f"   Address: {edited_lot['address']}")
            print(f"   City: {edited_lot['city']}")
            print(f"   State: {edited_lot['state']}")
            print(f"   Pin Code: {edited_lot['pin_code']}")
            print(f"   Price: ₹{edited_lot['price_per_hour']}")
            print(f"   Max Spots: {edited_lot['maximum_spots']}")
            print(f"   Available: {edited_lot['available_spots']}")
            print(f"   Occupied: {edited_lot['occupied_spots']}")
            
            # Verify all changes
            success = True
            if edited_lot['location_name'] != edit_data['location_name']:
                print("❌ Location name not updated")
                success = False
            if edited_lot['address'] != edit_data['address']:
                print("❌ Address not updated")
                success = False
            if edited_lot['city'] != edit_data['city']:
                print("❌ City not updated")
                success = False
            if edited_lot['state'] != edit_data['state']:
                print("❌ State not updated")
                success = False
            if edited_lot['pin_code'] != edit_data['pin_code']:
                print("❌ Pin code not updated")
                success = False
            if abs(float(edited_lot['price_per_hour']) - float(edit_data['price_per_hour'])) > 0.01:
                print("❌ Price not updated")
                success = False
            if edited_lot['maximum_spots'] != int(edit_data['maximum_spots']):
                print("❌ Maximum spots not updated")
                success = False
            if edited_lot['available_spots'] != int(edit_data['maximum_spots']):
                print("❌ Available spots not updated correctly")
                success = False
            
            if success:
                print("✅ ALL EDIT CHANGES VERIFIED SUCCESSFULLY!")
                return True
            else:
                print("❌ SOME EDIT CHANGES NOT APPLIED")
                return False
        else:
            print(f"❌ Verification API failed: {verify_response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Verification error: {str(e)}")
        return False

def main():
    print("🧪 COMPLETE PARKING LOT TEST SUITE")
    print("=" * 70)
    
    success = test_complete_parking_lot_functionality()
    
    print("\n📊 FINAL TEST SUMMARY")
    print("=" * 70)
    if success:
        print("🎉 PARKING LOT FUNCTIONALITY COMPLETELY WORKING!")
        print("✅ Admin authentication working")
        print("✅ Parking lot creation working")
        print("✅ City and state fields working in creation")
        print("✅ Parking spots auto-generation working")
        print("✅ Parking lot editing working")
        print("✅ City and state fields working in editing")
        print("✅ Spot count changes working")
        print("✅ All field updates verified")
        print("✅ API integration working perfectly")
        print("\n🔧 BOTH CREATE AND EDIT PARKING LOT FEATURES ARE FIXED!")
    else:
        print("❌ PARKING LOT FUNCTIONALITY HAS ISSUES")
        print("Please check the test output above for specific problems.")
    
    print(f"\nTest completed: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
