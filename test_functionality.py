#!/usr/bin/env python3
"""
Functionality Test Script for Vehicle Parking Management System
This script tests all major functionality with the created test data.
"""

import sys
import os
from datetime import datetime, timedelta
import pytz

# Add the current directory to Python path to import app modules
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import the app and models
from app import app, db, User, ParkingLot, ParkingSpot, Reservation

def test_functionality():
    """Test all major functionality of the application"""
    
    with app.app_context():
        print("🧪 Testing Vehicle Parking Management System Functionality...")
        print("=" * 60)
        
        # Test 1: Verify Users
        print("\n1️⃣ Testing User Management:")
        users = User.query.all()
        print(f"   📊 Total Users: {len(users)}")
        
        admin_user = User.query.filter_by(is_admin=True).first()
        regular_users = User.query.filter_by(is_admin=False).all()
        
        print(f"   👑 Admin Users: {len([u for u in users if u.is_admin])}")
        print(f"   👤 Regular Users: {len(regular_users)}")
        
        if admin_user:
            print(f"   ✅ Admin user exists: {admin_user.username} ({admin_user.email})")
        
        for user in regular_users[:3]:  # Show first 3 users
            print(f"   ✅ User: {user.username} | {user.email} | {user.city}, {user.state}")
        
        # Test 2: Verify Parking Lots
        print("\n2️⃣ Testing Parking Lot Management:")
        parking_lots = ParkingLot.query.all()
        print(f"   📊 Total Parking Lots: {len(parking_lots)}")
        
        for lot in parking_lots:
            available_spots = lot.get_available_spots_count()
            occupied_spots = lot.get_occupied_spots_count()
            print(f"   🏢 {lot.location_name} ({lot.city})")
            print(f"      💰 ₹{lot.price_per_hour}/hour | 🅿️ {available_spots}/{lot.maximum_spots} available")
        
        # Test 3: Verify Parking Spots
        print("\n3️⃣ Testing Parking Spot Management:")
        total_spots = ParkingSpot.query.count()
        available_spots = ParkingSpot.query.filter_by(status='A').count()
        occupied_spots = ParkingSpot.query.filter_by(status='O').count()
        
        print(f"   📊 Total Spots: {total_spots}")
        print(f"   🟢 Available: {available_spots}")
        print(f"   🔴 Occupied: {occupied_spots}")
        
        # Test 4: Create Sample Reservations
        print("\n4️⃣ Testing Booking System:")
        
        # Create a few sample bookings
        test_bookings = [
            {'user': 'john_doe', 'vehicle': 'MH01AB1234', 'lot_name': 'Phoenix Mall Parking'},
            {'user': 'jane_smith', 'vehicle': 'DL02CD5678', 'lot_name': 'Central Business District'},
            {'user': 'mike_wilson', 'vehicle': 'KA03EF9012', 'lot_name': 'Tech Park Parking'},
        ]
        
        created_reservations = []
        for booking in test_bookings:
            user = User.query.filter_by(username=booking['user']).first()
            lot = ParkingLot.query.filter_by(location_name=booking['lot_name']).first()
            
            if user and lot:
                # Find an available spot
                available_spot = ParkingSpot.query.filter_by(lot_id=lot.id, status='A').first()
                
                if available_spot:
                    # Create reservation
                    reservation = Reservation(
                        spot_id=available_spot.id,
                        user_id=user.id,
                        vehicle_number=booking['vehicle'],
                        status='booked'
                    )
                    
                    # Mark spot as occupied
                    available_spot.status = 'O'
                    
                    db.session.add(reservation)
                    created_reservations.append(reservation)
                    
                    print(f"   ✅ Booked: {booking['vehicle']} → {lot.location_name} (Spot: {available_spot.spot_number})")
        
        db.session.commit()
        print(f"   📊 Created {len(created_reservations)} test reservations")
        
        # Test 5: Verify Reservations
        print("\n5️⃣ Testing Reservation Management:")
        all_reservations = Reservation.query.all()
        print(f"   📊 Total Reservations: {len(all_reservations)}")
        
        for reservation in all_reservations:
            user = User.query.get(reservation.user_id)
            spot = ParkingSpot.query.get(reservation.spot_id)
            lot = ParkingLot.query.get(spot.lot_id)
            
            print(f"   🎫 {reservation.vehicle_number} | {user.username} | {lot.location_name} | {reservation.status}")
        
        # Test 6: Test Cost Calculation (simulate some completed bookings)
        print("\n6️⃣ Testing Cost Calculation:")
        ist = pytz.timezone('Asia/Kolkata')
        
        # Simulate completing a booking
        if created_reservations:
            test_reservation = created_reservations[0]
            
            # Set parking and leaving times (2 hours parking)
            now = datetime.now(ist)
            test_reservation.parking_timestamp = now - timedelta(hours=2)
            test_reservation.leaving_timestamp = now
            test_reservation.status = 'completed'
            
            # Calculate cost
            cost = test_reservation.calculate_cost()
            test_reservation.parking_cost = cost
            
            db.session.commit()
            
            spot = ParkingSpot.query.get(test_reservation.spot_id)
            lot = ParkingLot.query.get(spot.lot_id)
            
            print(f"   💰 Sample Cost Calculation:")
            print(f"      Vehicle: {test_reservation.vehicle_number}")
            print(f"      Duration: 2 hours")
            print(f"      Rate: ₹{lot.price_per_hour}/hour")
            print(f"      Total Cost: ₹{cost}")
        
        # Test 7: Database Integrity
        print("\n7️⃣ Testing Database Integrity:")
        
        # Check foreign key relationships
        orphaned_spots = ParkingSpot.query.filter(~ParkingSpot.lot_id.in_(
            db.session.query(ParkingLot.id)
        )).count()
        
        orphaned_reservations = Reservation.query.filter(~Reservation.spot_id.in_(
            db.session.query(ParkingSpot.id)
        )).count()
        
        print(f"   🔗 Orphaned Spots: {orphaned_spots}")
        print(f"   🔗 Orphaned Reservations: {orphaned_reservations}")
        
        if orphaned_spots == 0 and orphaned_reservations == 0:
            print("   ✅ Database integrity maintained")
        else:
            print("   ❌ Database integrity issues found")
        
        # Final Summary
        print("\n" + "=" * 60)
        print("🎉 FUNCTIONALITY TEST COMPLETED!")
        print("=" * 60)
        print(f"📊 Summary:")
        print(f"   👥 Users: {len(users)} ({len([u for u in users if u.is_admin])} admin, {len(regular_users)} regular)")
        print(f"   🏢 Parking Lots: {len(parking_lots)}")
        print(f"   🅿️ Parking Spots: {total_spots} ({ParkingSpot.query.filter_by(status='A').count()} available)")
        print(f"   🎫 Reservations: {Reservation.query.count()}")
        
        print(f"\n🌐 Application is ready for testing at: http://127.0.0.1:5000")
        print(f"🔑 Login with any of the test credentials provided earlier")

if __name__ == '__main__':
    test_functionality()
