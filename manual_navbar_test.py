#!/usr/bin/env python3
"""
Manual Navbar Test - Quick verification of all navbar links
"""

import requests
import sys

BASE_URL = "http://127.0.0.1:5000"

def test_navbar_links():
    """Test all navbar links manually"""
    print("🧪 MANUAL NAVBAR FUNCTIONALITY TEST")
    print("=" * 50)
    
    session = requests.Session()
    
    # Test 1: Login as admin
    print("\n1. Testing Admin Login...")
    login_data = {'username': 'admin', 'password': 'admin123'}
    response = session.post(f"{BASE_URL}/login", data=login_data)
    print(f"   Login Status: {response.status_code}")
    
    # Test 2: Admin navbar links
    print("\n2. Testing Admin Navbar Links...")
    admin_links = [
        ("/admin/dashboard", "Dashboard"),
        ("/admin/users", "Users"),
        ("/admin/search_spot", "Search Spots"),  # Corrected URL
        ("/admin/reports", "Reports"),
        ("/profile", "Profile")
    ]
    
    for url, name in admin_links:
        response = session.get(f"{BASE_URL}{url}")
        status = "✅" if response.status_code == 200 else "❌"
        print(f"   {status} {name}: {response.status_code}")
    
    # Test 3: Logout and test user
    print("\n3. Logging out and testing User Login...")
    session.get(f"{BASE_URL}/logout")
    
    login_data = {'username': 'john_doe', 'password': 'password123'}
    response = session.post(f"{BASE_URL}/login", data=login_data)
    print(f"   User Login Status: {response.status_code}")
    
    # Test 4: User navbar links
    print("\n4. Testing User Navbar Links...")
    user_links = [
        ("/user/dashboard", "Dashboard"),
        ("/profile", "Profile")
    ]
    
    for url, name in user_links:
        response = session.get(f"{BASE_URL}{url}")
        status = "✅" if response.status_code == 200 else "❌"
        print(f"   {status} {name}: {response.status_code}")
    
    # Test 5: Test protection (logout first)
    print("\n5. Testing Route Protection (after logout)...")
    session.get(f"{BASE_URL}/logout")
    
    protected_routes = [
        ("/admin/dashboard", "Admin Dashboard"),
        ("/user/dashboard", "User Dashboard"),
        ("/profile", "Profile")
    ]
    
    for url, name in protected_routes:
        response = session.get(f"{BASE_URL}{url}", allow_redirects=False)
        if response.status_code == 302:
            status = "✅ Protected"
        else:
            status = f"❌ Not Protected ({response.status_code})"
        print(f"   {status} {name}")

if __name__ == "__main__":
    test_navbar_links()
