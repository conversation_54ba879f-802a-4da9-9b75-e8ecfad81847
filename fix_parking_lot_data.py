#!/usr/bin/env python3
"""
Fix Parking Lot Data
Updates existing parking lots to have city and state values
"""

import sys
import os

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import app, db, ParkingLot
from datetime import datetime

def fix_parking_lot_data():
    """Update existing parking lots with default city and state values"""
    print("🔧 FIXING PARKING LOT DATA")
    print("=" * 50)
    print(f"Started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()

    with app.app_context():
        try:
            # Get all parking lots
            lots = ParkingLot.query.all()
            print(f"Found {len(lots)} parking lots to check")
            print()

            updated_count = 0
            for lot in lots:
                print(f"Checking lot ID {lot.id}: {lot.location_name}")
                
                needs_update = False
                
                # Check if city is missing or empty
                if not lot.city:
                    print(f"  ❌ Missing city field")
                    lot.city = "Mumbai"  # Default city
                    needs_update = True
                else:
                    print(f"  ✅ City: {lot.city}")
                
                # Check if state is missing or empty
                if not lot.state:
                    print(f"  ❌ Missing state field")
                    lot.state = "Maharashtra"  # Default state
                    needs_update = True
                else:
                    print(f"  ✅ State: {lot.state}")
                
                if needs_update:
                    print(f"  🔄 Updating lot {lot.id} with default values")
                    updated_count += 1
                else:
                    print(f"  ✅ Lot {lot.id} already has city and state")
                
                print()

            if updated_count > 0:
                # Commit all changes
                db.session.commit()
                print(f"✅ Successfully updated {updated_count} parking lots")
            else:
                print("✅ All parking lots already have city and state values")

            print()
            print("📊 VERIFICATION")
            print("-" * 30)
            
            # Verify all lots now have city and state
            lots = ParkingLot.query.all()
            all_good = True
            
            for lot in lots:
                if not lot.city or not lot.state:
                    print(f"❌ Lot {lot.id} still missing data: city='{lot.city}', state='{lot.state}'")
                    all_good = False
                else:
                    print(f"✅ Lot {lot.id}: {lot.location_name} - {lot.city}, {lot.state}")
            
            if all_good:
                print("\n🎉 ALL PARKING LOTS NOW HAVE CITY AND STATE DATA!")
                return True
            else:
                print("\n❌ Some parking lots still have missing data")
                return False

        except Exception as e:
            db.session.rollback()
            print(f"❌ Error updating parking lots: {str(e)}")
            return False

def test_api_after_fix():
    """Test the API to make sure city and state are now returned"""
    print("\n🧪 TESTING API AFTER FIX")
    print("=" * 50)
    
    import requests
    
    try:
        response = requests.get("http://127.0.0.1:5000/api/parking_lots")
        if response.status_code == 200:
            data = response.json()
            lots = data.get('parking_lots', [])
            
            print(f"API returned {len(lots)} parking lots")
            print()
            
            all_have_city_state = True
            for lot in lots:
                city = lot.get('city', 'NOT FOUND')
                state = lot.get('state', 'NOT FOUND')
                
                print(f"Lot {lot['id']}: {lot['location_name']}")
                print(f"  City: {city}")
                print(f"  State: {state}")
                
                if city == 'NOT FOUND' or state == 'NOT FOUND':
                    all_have_city_state = False
                    print("  ❌ Missing city or state in API")
                else:
                    print("  ✅ Has city and state in API")
                print()
            
            if all_have_city_state:
                print("🎉 ALL LOTS NOW HAVE CITY AND STATE IN API!")
                return True
            else:
                print("❌ Some lots still missing city/state in API")
                return False
        else:
            print(f"❌ API request failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ API test error: {str(e)}")
        return False

def main():
    print("🔧 PARKING LOT DATA FIX UTILITY")
    print("=" * 60)
    
    # Fix the data
    fix_success = fix_parking_lot_data()
    
    if fix_success:
        # Test the API
        api_success = test_api_after_fix()
        
        print("\n📊 FINAL SUMMARY")
        print("=" * 60)
        if api_success:
            print("🎉 PARKING LOT DATA SUCCESSFULLY FIXED!")
            print("✅ All parking lots now have city and state values")
            print("✅ API is returning city and state fields")
            print("✅ Edit parking lot functionality should now work")
        else:
            print("⚠️ Data was fixed but API still has issues")
    else:
        print("\n📊 FINAL SUMMARY")
        print("=" * 60)
        print("❌ Failed to fix parking lot data")
    
    print(f"\nCompleted: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
