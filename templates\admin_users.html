{% extends "base.html" %}

{% block title %}Users - Admin Dashboard{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <h1 class="h3 mb-0">
                <i class="bi bi-people"></i> Registered Users
            </h1>
            <p class="text-muted">View and manage all registered users</p>
        </div>
    </div>

    <!-- Users Table -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-list-ul"></i> All Users ({{ users|length }} total)
                    </h5>
                </div>
                <div class="card-body">
                    {% if users %}
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Username</th>
                                    <th>Email</th>
                                    <th>Phone</th>
                                    <th>City</th>
                                    <th>State</th>
                                    <th>Registered</th>
                                    <th>Total Reservations</th>
                                    <th>Active Reservations</th>
                                    <th>Total Spent</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for user in users %}
                                <tr>
                                    <td>{{ user.id }}</td>
                                    <td><strong>{{ user.username }}</strong></td>
                                    <td>{{ user.email }}</td>
                                    <td>{{ user.phone_number if user.phone_number else 'N/A' }}</td>
                                    <td>{{ user.city if user.city else 'N/A' }}</td>
                                    <td>{{ user.state if user.state else 'N/A' }}</td>
                                    <td>{{ user.created_at.strftime('%d/%m/%Y') }}</td>
                                    <td>
                                        <span class="badge bg-primary">{{ user.reservations|length }}</span>
                                    </td>
                                    <td>
                                        <span class="badge bg-warning">
                                            {{ user.reservations|selectattr("status", "in", ["booked", "parked"])|list|length }}
                                        </span>
                                    </td>
                                    <td>
                                        ₹{{ "%.2f"|format(user.reservations|selectattr("status", "equalto", "completed")|map(attribute="parking_cost")|sum) }}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-4">
                        <i class="bi bi-people display-4 text-muted"></i>
                        <p class="text-muted mt-2">No users registered yet.</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
