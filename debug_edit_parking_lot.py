#!/usr/bin/env python3
"""
Debug Edit Parking Lot Functionality
Detailed debugging to identify the exact issue with edit parking lot
"""

import requests
import json
from datetime import datetime

BASE_URL = "http://127.0.0.1:5000"

def debug_edit_parking_lot():
    """Debug parking lot editing step by step"""
    print("🔍 DEBUGGING EDIT PARKING LOT")
    print("=" * 50)
    print(f"Testing at: {BASE_URL}")
    print(f"Debug started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()

    # Create a session to maintain cookies
    session = requests.Session()

    # Step 1: Admin login
    print("🔐 Step 1: Admin Login")
    print("-" * 30)
    
    login_data = {
        'username': 'admin',
        'password': 'admin123'
    }
    
    try:
        login_response = session.post(f"{BASE_URL}/login", data=login_data, allow_redirects=False)
        print(f"Login response status: {login_response.status_code}")
        print(f"Login response headers: {dict(login_response.headers)}")
        
        if login_response.status_code == 302:
            print("✅ Admin login successful")
        else:
            print(f"❌ Admin login failed: {login_response.status_code}")
            print(f"Response text: {login_response.text[:500]}")
            return False
    except Exception as e:
        print(f"❌ Login error: {str(e)}")
        return False

    print()

    # Step 2: Get admin dashboard to check if parking lots are loaded
    print("🏠 Step 2: Check Admin Dashboard")
    print("-" * 30)
    
    try:
        dashboard_response = session.get(f"{BASE_URL}/admin/dashboard")
        print(f"Dashboard response status: {dashboard_response.status_code}")
        
        if dashboard_response.status_code == 200:
            print("✅ Admin dashboard accessible")
            # Check if edit modals are present
            if "editModal" in dashboard_response.text:
                print("✅ Edit modals found in dashboard")
            else:
                print("❌ Edit modals NOT found in dashboard")
        else:
            print(f"❌ Dashboard access failed: {dashboard_response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Dashboard error: {str(e)}")
        return False

    print()

    # Step 3: Get parking lots via API
    print("📋 Step 3: Get Parking Lots via API")
    print("-" * 30)
    
    try:
        api_response = session.get(f"{BASE_URL}/api/parking_lots")
        print(f"API response status: {api_response.status_code}")
        
        if api_response.status_code == 200:
            data = api_response.json()
            parking_lots = data.get('parking_lots', [])
            print(f"✅ Found {len(parking_lots)} parking lots")
            
            if parking_lots:
                test_lot = parking_lots[0]
                lot_id = test_lot['id']
                print(f"Using parking lot ID: {lot_id}")
                print(f"Current name: {test_lot['location_name']}")
                print(f"Current address: {test_lot['address']}")
                print(f"Current city: {test_lot.get('city', 'NOT FOUND')}")
                print(f"Current state: {test_lot.get('state', 'NOT FOUND')}")
                print(f"Current price: ₹{test_lot['price_per_hour']}")
                print(f"Current spots: {test_lot['maximum_spots']}")
            else:
                print("❌ No parking lots found")
                return False
        else:
            print(f"❌ API request failed: {api_response.status_code}")
            return False
    except Exception as e:
        print(f"❌ API error: {str(e)}")
        return False

    print()

    # Step 4: Test edit request with detailed logging
    print("✏️ Step 4: Test Edit Request")
    print("-" * 30)
    
    # Prepare edit data
    edit_data = {
        'location_name': 'DEBUG Test Mall',
        'address': 'DEBUG Test Address 123',
        'city': 'DEBUG City',
        'state': 'DEBUG State',
        'pin_code': '123456',
        'price_per_hour': '99.99',
        'maximum_spots': str(test_lot['maximum_spots'])  # Keep same spot count
    }
    
    print("Edit data being sent:")
    for key, value in edit_data.items():
        print(f"  {key}: {value}")
    
    try:
        edit_url = f"{BASE_URL}/admin/edit_lot/{lot_id}"
        print(f"Edit URL: {edit_url}")
        
        edit_response = session.post(edit_url, data=edit_data, allow_redirects=False)
        print(f"Edit response status: {edit_response.status_code}")
        print(f"Edit response headers: {dict(edit_response.headers)}")
        
        if edit_response.status_code == 302:
            print("✅ Edit request accepted (302 redirect)")
            redirect_location = edit_response.headers.get('Location', 'No location header')
            print(f"Redirect location: {redirect_location}")
        elif edit_response.status_code == 200:
            print("⚠️ Edit request returned 200 (should be 302)")
            print(f"Response text preview: {edit_response.text[:500]}")
        else:
            print(f"❌ Edit request failed: {edit_response.status_code}")
            print(f"Response text: {edit_response.text[:500]}")
            return False
            
    except Exception as e:
        print(f"❌ Edit request error: {str(e)}")
        return False

    print()

    # Step 5: Verify changes via API
    print("🔍 Step 5: Verify Changes via API")
    print("-" * 30)
    
    try:
        # Wait a moment for database to update
        import time
        time.sleep(1)
        
        verify_response = session.get(f"{BASE_URL}/api/parking_lots")
        if verify_response.status_code == 200:
            data = verify_response.json()
            parking_lots = data.get('parking_lots', [])
            
            # Find our edited lot
            edited_lot = None
            for lot in parking_lots:
                if lot['id'] == lot_id:
                    edited_lot = lot
                    break
            
            if edited_lot:
                print("📊 Current lot data after edit:")
                print(f"  ID: {edited_lot['id']}")
                print(f"  Name: {edited_lot['location_name']}")
                print(f"  Address: {edited_lot['address']}")
                print(f"  City: {edited_lot.get('city', 'NOT FOUND')}")
                print(f"  State: {edited_lot.get('state', 'NOT FOUND')}")
                print(f"  Pin Code: {edited_lot['pin_code']}")
                print(f"  Price: ₹{edited_lot['price_per_hour']}")
                print(f"  Spots: {edited_lot['maximum_spots']}")
                
                # Check if changes were applied
                changes_applied = True
                if edited_lot['location_name'] != edit_data['location_name']:
                    print("❌ Location name not updated")
                    changes_applied = False
                if edited_lot['address'] != edit_data['address']:
                    print("❌ Address not updated")
                    changes_applied = False
                if edited_lot.get('city') != edit_data['city']:
                    print("❌ City not updated")
                    changes_applied = False
                if edited_lot.get('state') != edit_data['state']:
                    print("❌ State not updated")
                    changes_applied = False
                if edited_lot['pin_code'] != edit_data['pin_code']:
                    print("❌ Pin code not updated")
                    changes_applied = False
                if abs(float(edited_lot['price_per_hour']) - float(edit_data['price_per_hour'])) > 0.01:
                    print("❌ Price not updated")
                    changes_applied = False
                
                if changes_applied:
                    print("✅ ALL CHANGES APPLIED SUCCESSFULLY!")
                    return True
                else:
                    print("❌ SOME CHANGES NOT APPLIED")
                    return False
            else:
                print("❌ Edited lot not found in API response")
                return False
        else:
            print(f"❌ Verification API failed: {verify_response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Verification error: {str(e)}")
        return False

def main():
    print("🔧 EDIT PARKING LOT DEBUG SUITE")
    print("=" * 60)
    
    success = debug_edit_parking_lot()
    
    print("\n📊 DEBUG SUMMARY")
    print("=" * 60)
    if success:
        print("🎉 EDIT PARKING LOT IS WORKING!")
        print("The issue might be in the user interface or browser.")
    else:
        print("❌ EDIT PARKING LOT HAS ISSUES")
        print("Check the debug output above for specific problems.")
    
    print(f"\nDebug completed: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
