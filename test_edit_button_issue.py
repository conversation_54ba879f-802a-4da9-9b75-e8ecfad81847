#!/usr/bin/env python3
"""
Test Edit Button Issue in Admin Dashboard
Checks if edit buttons are properly configured and working
"""

import requests
from datetime import datetime
import re

BASE_URL = "http://127.0.0.1:5000"

def test_edit_button_issue():
    """Test edit button functionality in admin dashboard"""
    print("🔘 TESTING EDIT BUTTON ISSUE")
    print("=" * 50)
    print(f"Testing at: {BASE_URL}")
    print(f"Test started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()

    # Create a session to maintain cookies
    session = requests.Session()

    # Step 1: Admin login
    print("🔐 Step 1: Admin Login")
    print("-" * 30)
    
    login_data = {
        'username': 'admin',
        'password': 'admin123'
    }
    
    try:
        login_response = session.post(f"{BASE_URL}/login", data=login_data, allow_redirects=False)
        if login_response.status_code == 302:
            print("✅ Admin login successful")
        else:
            print(f"❌ Admin login failed: {login_response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Login error: {str(e)}")
        return False

    print()

    # Step 2: Get admin dashboard and analyze edit buttons
    print("🏠 Step 2: Analyze Edit Buttons in Dashboard")
    print("-" * 30)
    
    try:
        dashboard_response = session.get(f"{BASE_URL}/admin/dashboard")
        if dashboard_response.status_code == 200:
            html_content = dashboard_response.text
            print("✅ Admin dashboard loaded successfully")
            
            # Check for Bootstrap CSS and JS
            if 'bootstrap' in html_content:
                print("✅ Bootstrap CSS/JS found")
            else:
                print("❌ Bootstrap CSS/JS missing")
                
            # Check for Bootstrap Icons
            if 'bootstrap-icons' in html_content or 'bi-pencil' in html_content:
                print("✅ Bootstrap Icons found")
            else:
                print("❌ Bootstrap Icons missing")
            
            # Find all edit buttons
            edit_button_pattern = r'data-bs-target="#editModal(\d+)"'
            edit_buttons = re.findall(edit_button_pattern, html_content)
            print(f"✅ Found {len(edit_buttons)} edit buttons")
            
            # Find all edit modals
            edit_modal_pattern = r'id="editModal(\d+)"'
            edit_modals = re.findall(edit_modal_pattern, html_content)
            print(f"✅ Found {len(edit_modals)} edit modals")
            
            # Check if buttons match modals
            if set(edit_buttons) == set(edit_modals):
                print("✅ All edit buttons have matching modals")
            else:
                print("❌ Mismatch between edit buttons and modals")
                print(f"   Button IDs: {sorted(edit_buttons)}")
                print(f"   Modal IDs: {sorted(edit_modals)}")
            
            # Check for specific issues
            issues_found = []
            
            # Check for proper Bootstrap modal structure
            if 'class="modal fade"' not in html_content:
                issues_found.append("Missing Bootstrap modal classes")
            
            # Check for proper button attributes
            if 'data-bs-toggle="modal"' not in html_content:
                issues_found.append("Missing data-bs-toggle attribute")
            
            # Check for form structure in modals
            if 'method="POST"' not in html_content:
                issues_found.append("Missing POST method in forms")
            
            # Check for required form fields
            required_fields = ['location_name', 'address', 'city', 'state', 'pin_code', 'price_per_hour', 'maximum_spots']
            for field in required_fields:
                if f'name="{field}"' not in html_content:
                    issues_found.append(f"Missing form field: {field}")
            
            if issues_found:
                print("❌ Issues found:")
                for issue in issues_found:
                    print(f"   - {issue}")
                return False
            else:
                print("✅ No structural issues found with edit buttons")
                
            # Check for JavaScript errors (basic check)
            if 'console.error' in html_content or 'onerror' in html_content:
                print("⚠️ Potential JavaScript errors detected")
            else:
                print("✅ No obvious JavaScript errors")
                
            return True
            
        else:
            print(f"❌ Dashboard access failed: {dashboard_response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Dashboard error: {str(e)}")
        return False

def test_user_booking_dates():
    """Test if users can book spots for any date"""
    print("\n📅 TESTING USER BOOKING DATE RESTRICTIONS")
    print("=" * 50)
    
    session = requests.Session()
    
    # Login as a regular user first
    print("👤 Step 1: Regular User Login")
    print("-" * 30)
    
    # Get a regular user from API
    admin_session = requests.Session()
    admin_session.post(f"{BASE_URL}/login", data={'username': 'admin', 'password': 'admin123'})
    
    try:
        users_response = admin_session.get(f"{BASE_URL}/api/users")
        if users_response.status_code == 200:
            users_data = users_response.json()
            users = users_data.get('users', [])
            
            # Find a non-admin user
            regular_user = None
            for user in users:
                if user['username'] != 'admin':
                    regular_user = user
                    break
            
            if regular_user:
                print(f"Found regular user: {regular_user['username']}")
                
                # Try to login as regular user (we don't have their password, so let's check the booking page structure)
                # Instead, let's check the booking form structure
                
                print("\n📝 Step 2: Check Booking Form Structure")
                print("-" * 30)
                
                # Get the main page to see booking options
                main_response = session.get(f"{BASE_URL}/")
                if main_response.status_code == 200:
                    html_content = main_response.text
                    
                    # Check for date input fields
                    if 'type="date"' in html_content:
                        print("✅ Date input fields found")
                        
                        # Check for date restrictions
                        if 'min=' in html_content:
                            print("✅ Minimum date restriction found")
                        else:
                            print("❌ No minimum date restriction - users can book past dates")
                        
                        if 'max=' in html_content:
                            print("✅ Maximum date restriction found")
                        else:
                            print("⚠️ No maximum date restriction - users can book far future dates")
                        
                        # Look for JavaScript date validation
                        if 'new Date()' in html_content or 'getDate()' in html_content:
                            print("✅ JavaScript date validation found")
                        else:
                            print("⚠️ No JavaScript date validation found")
                        
                        return True
                    else:
                        print("❌ No date input fields found in booking form")
                        return False
                else:
                    print(f"❌ Main page access failed: {main_response.status_code}")
                    return False
            else:
                print("❌ No regular users found")
                return False
        else:
            print(f"❌ Users API failed: {users_response.status_code}")
            return False
    except Exception as e:
        print(f"❌ User booking test error: {str(e)}")
        return False

def main():
    print("🔍 ADMIN DASHBOARD ISSUES INVESTIGATION")
    print("=" * 70)
    
    # Test 1: Edit button issue
    edit_success = test_edit_button_issue()
    
    # Test 2: User booking date restrictions
    booking_success = test_user_booking_dates()
    
    print("\n📊 INVESTIGATION SUMMARY")
    print("=" * 70)
    
    print("1. EDIT BUTTON ISSUE:")
    if edit_success:
        print("   ✅ Edit buttons appear to be properly configured")
        print("   ✅ Bootstrap modals structure is correct")
        print("   ✅ All required form fields are present")
        print("   💡 If edit buttons still don't work, it might be a browser/JavaScript issue")
    else:
        print("   ❌ Edit buttons have structural issues")
        print("   🔧 Check the specific issues listed above")
    
    print("\n2. USER BOOKING DATE RESTRICTIONS:")
    if booking_success:
        print("   📅 Date input fields are present in booking forms")
        print("   ⚠️ Check the specific date restriction details above")
    else:
        print("   ❌ Issues found with booking date functionality")
    
    print(f"\nInvestigation completed: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
