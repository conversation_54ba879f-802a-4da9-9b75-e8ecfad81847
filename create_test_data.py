#!/usr/bin/env python3
"""
Test Data Creation Script for Vehicle Parking Management System
This script creates comprehensive test data including users, parking lots, and reservations.
"""

import sys
import os
from datetime import datetime, timedelta
import pytz
from werkzeug.security import generate_password_hash

# Add the current directory to Python path to import app modules
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import the app and models
from app import app, db, User, ParkingLot, ParkingSpot, Reservation

def create_test_data():
    """Create comprehensive test data for the application"""
    
    with app.app_context():
        print("🚀 Creating test data for Vehicle Parking Management System...")
        
        # Clear existing data (except admin)
        print("🧹 Clearing existing test data...")
        Reservation.query.filter(Reservation.user_id != 1).delete()  # Keep admin reservations if any
        ParkingSpot.query.delete()
        ParkingLot.query.delete()
        User.query.filter(User.username != 'admin').delete()
        db.session.commit()
        
        # Create test users
        print("👥 Creating test users...")
        test_users = [
            {
                'username': 'john_doe',
                'email': '<EMAIL>',
                'phone_number': '9876543210',
                'address': '123 Main Street, Apartment 4B',
                'city': 'Mumbai',
                'state': 'Maharashtra',
                'pincode': '400001',
                'password': 'password123'
            },
            {
                'username': 'jane_smith',
                'email': '<EMAIL>',
                'phone_number': '9876543211',
                'address': '456 Oak Avenue, Villa 12',
                'city': 'Delhi',
                'state': 'Delhi',
                'pincode': '110001',
                'password': 'password123'
            },
            {
                'username': 'mike_wilson',
                'email': '<EMAIL>',
                'phone_number': '9876543212',
                'address': '789 Pine Road, House 34',
                'city': 'Bangalore',
                'state': 'Karnataka',
                'pincode': '560001',
                'password': 'password123'
            },
            {
                'username': 'sarah_johnson',
                'email': '<EMAIL>',
                'phone_number': '9876543213',
                'address': '321 Cedar Lane, Flat 7A',
                'city': 'Chennai',
                'state': 'Tamil Nadu',
                'pincode': '600001',
                'password': 'password123'
            },
            {
                'username': 'david_brown',
                'email': '<EMAIL>',
                'phone_number': '9876543214',
                'address': '654 Elm Street, Bungalow 5',
                'city': 'Pune',
                'state': 'Maharashtra',
                'pincode': '411001',
                'password': 'password123'
            }
        ]
        
        created_users = []
        for user_data in test_users:
            user = User(
                username=user_data['username'],
                email=user_data['email'],
                phone_number=user_data['phone_number'],
                address=user_data['address'],
                city=user_data['city'],
                state=user_data['state'],
                pincode=user_data['pincode'],
                password_hash=generate_password_hash(user_data['password']),
                is_admin=False
            )
            db.session.add(user)
            created_users.append(user)
        
        db.session.commit()
        print(f"✅ Created {len(created_users)} test users")
        
        # Create test parking lots
        print("🏢 Creating test parking lots...")
        test_parking_lots = [
            {
                'location_name': 'Phoenix Mall Parking',
                'address': '142 Palladium Mall Complex',
                'city': 'Mumbai',
                'state': 'Maharashtra',
                'pin_code': '400001',
                'price_per_hour': 50.0,
                'maximum_spots': 25
            },
            {
                'location_name': 'Central Business District',
                'address': '88 Corporate Tower Basement',
                'city': 'Delhi',
                'state': 'Delhi',
                'pin_code': '110001',
                'price_per_hour': 75.0,
                'maximum_spots': 40
            },
            {
                'location_name': 'Tech Park Parking',
                'address': '234 IT Corridor, Electronic City',
                'city': 'Bangalore',
                'state': 'Karnataka',
                'pin_code': '560001',
                'price_per_hour': 60.0,
                'maximum_spots': 30
            },
            {
                'location_name': 'Marina Beach Parking',
                'address': '567 Beach Road, Near Lighthouse',
                'city': 'Chennai',
                'state': 'Tamil Nadu',
                'pin_code': '600001',
                'price_per_hour': 40.0,
                'maximum_spots': 20
            },
            {
                'location_name': 'Airport Terminal Parking',
                'address': '999 Airport Road, Terminal 2',
                'city': 'Pune',
                'state': 'Maharashtra',
                'pin_code': '411001',
                'price_per_hour': 100.0,
                'maximum_spots': 50
            },
            {
                'location_name': 'Shopping Complex',
                'address': '123 Market Street, Central Plaza',
                'city': 'Mumbai',
                'state': 'Maharashtra',
                'pin_code': '400002',
                'price_per_hour': 45.0,
                'maximum_spots': 15
            }
        ]
        
        created_lots = []
        for lot_data in test_parking_lots:
            lot = ParkingLot(
                location_name=lot_data['location_name'],
                address=lot_data['address'],
                city=lot_data['city'],
                state=lot_data['state'],
                pin_code=lot_data['pin_code'],
                price_per_hour=lot_data['price_per_hour'],
                maximum_spots=lot_data['maximum_spots']
            )
            db.session.add(lot)
            created_lots.append(lot)
        
        db.session.commit()
        print(f"✅ Created {len(created_lots)} test parking lots")
        
        # Create parking spots for each lot
        print("🅿️ Creating parking spots...")
        total_spots = 0
        for lot in created_lots:
            for i in range(1, lot.maximum_spots + 1):
                spot = ParkingSpot(
                    lot_id=lot.id,
                    spot_number=f"S{i:03d}",
                    status='A'  # All spots start as available
                )
                db.session.add(spot)
                total_spots += 1
        
        db.session.commit()
        print(f"✅ Created {total_spots} parking spots")
        
        print("🎉 Test data creation completed successfully!")
        print("\n📊 Summary:")
        print(f"   👥 Users: {len(created_users)} (+ 1 admin)")
        print(f"   🏢 Parking Lots: {len(created_lots)}")
        print(f"   🅿️ Parking Spots: {total_spots}")
        print("\n🔑 Test Login Credentials:")
        print("   Admin: username='admin', password='admin123'")
        print("   Users: username='john_doe', password='password123'")
        print("          username='jane_smith', password='password123'")
        print("          username='mike_wilson', password='password123'")
        print("          username='sarah_johnson', password='password123'")
        print("          username='david_brown', password='password123'")
        print("\n🌐 Access the application at: http://127.0.0.1:5000")

if __name__ == '__main__':
    create_test_data()
