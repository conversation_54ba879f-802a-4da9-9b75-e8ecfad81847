#!/usr/bin/env python3
"""
Comprehensive Test Suite for Vehicle Parking Management System
Tests all features, functionality, forms, and user flows
"""

import requests
import json
import sys
from urllib.parse import urljoin

BASE_URL = "http://127.0.0.1:5000"

class ComprehensiveTestSuite:
    def __init__(self):
        self.session = requests.Session()
        self.test_results = []
        self.total_tests = 0
        self.passed_tests = 0
        
    def log_test(self, test_name, status, details=""):
        """Log test results"""
        self.total_tests += 1
        if status:
            self.passed_tests += 1
        
        status_symbol = "✅" if status else "❌"
        self.test_results.append({
            'name': test_name,
            'status': status,
            'details': details
        })
        print(f"{status_symbol} {test_name}: {details}")
        
    def test_url(self, url, expected_status=200, test_name="", method="GET", data=None):
        """Test if a URL is accessible"""
        try:
            if method == "POST":
                response = self.session.post(urljoin(BASE_URL, url), data=data)
            else:
                response = self.session.get(urljoin(BASE_URL, url))
            
            success = response.status_code == expected_status
            details = f"Status: {response.status_code}"
            if not success and response.status_code == 302:
                details += f" (Redirect)"
            self.log_test(test_name or f"URL: {url}", success, details)
            return success, response
        except Exception as e:
            self.log_test(test_name or f"URL: {url}", False, f"Error: {str(e)}")
            return False, None
            
    def login_user(self, username, password):
        """Login with given credentials"""
        login_data = {
            'username': username,
            'password': password
        }
        try:
            response = self.session.post(urljoin(BASE_URL, '/login'), data=login_data)
            success = response.status_code in [200, 302]
            self.log_test(f"Login as {username}", success, f"Status: {response.status_code}")
            return success
        except Exception as e:
            self.log_test(f"Login as {username}", False, f"Error: {str(e)}")
            return False
            
    def logout(self):
        """Logout current user"""
        try:
            response = self.session.get(urljoin(BASE_URL, '/logout'))
            success = response.status_code in [200, 302]
            self.log_test("Logout", success, f"Status: {response.status_code}")
            return success
        except Exception as e:
            self.log_test("Logout", False, f"Error: {str(e)}")
            return False
            
    def test_basic_pages(self):
        """Test basic page accessibility"""
        print("\n🌐 TESTING BASIC PAGES")
        print("=" * 50)
        
        basic_pages = [
            ("/", "Home Page"),
            ("/login", "Login Page"),
            ("/register", "Register Page"),
            ("/forgot_password", "Forgot Password Page")
        ]
        
        for url, name in basic_pages:
            self.test_url(url, test_name=name)
            
    def test_authentication_flow(self):
        """Test complete authentication flow"""
        print("\n🔐 TESTING AUTHENTICATION FLOW")
        print("=" * 50)
        
        # Test login with valid credentials
        self.login_user("admin", "admin123")
        
        # Test accessing protected page
        self.test_url("/admin/dashboard", test_name="Access Admin Dashboard After Login")
        
        # Test logout
        self.logout()
        
        # Test accessing protected page after logout
        self.test_url("/admin/dashboard", expected_status=302, test_name="Admin Dashboard After Logout (Should Redirect)")
        
        # Test login with invalid credentials
        invalid_login = self.test_url("/login", method="POST", data={
            'username': 'invalid_user',
            'password': 'wrong_password'
        }, test_name="Login with Invalid Credentials")
        
    def test_forgot_password_flow(self):
        """Test forgot password functionality"""
        print("\n🔑 TESTING FORGOT PASSWORD FLOW")
        print("=" * 50)
        
        # Test forgot password page
        self.test_url("/forgot_password", test_name="Forgot Password Page Access")
        
        # Test forgot password with valid email
        self.test_url("/forgot_password", method="POST", data={
            'email': '<EMAIL>'
        }, test_name="Forgot Password with Valid Email")
        
        # Test forgot password with invalid email
        self.test_url("/forgot_password", method="POST", data={
            'email': '<EMAIL>'
        }, test_name="Forgot Password with Invalid Email")
        
    def test_admin_functionality(self):
        """Test admin-specific functionality"""
        print("\n👑 TESTING ADMIN FUNCTIONALITY")
        print("=" * 50)
        
        # Login as admin
        if not self.login_user("admin", "admin123"):
            print("❌ Cannot test admin functionality - login failed")
            return
            
        # Test admin pages
        admin_pages = [
            ("/admin/dashboard", "Admin Dashboard"),
            ("/admin/users", "Admin Users Page"),
            ("/admin/search_spot", "Admin Search Spots"),
            ("/admin/reports", "Admin Reports"),
            ("/profile", "Admin Profile")
        ]
        
        for url, name in admin_pages:
            self.test_url(url, test_name=f"Admin - {name}")
            
        # Test admin API endpoints
        api_endpoints = [
            ("/admin/chart", "Admin Chart API"),
            ("/api/parking_lots", "Parking Lots API"),
            ("/api/parking_spots", "Parking Spots API"),
            ("/api/reservations", "Reservations API")
        ]
        
        for url, name in api_endpoints:
            self.test_url(url, test_name=f"API - {name}")
            
        self.logout()
        
    def test_user_functionality(self):
        """Test regular user functionality"""
        print("\n👤 TESTING USER FUNCTIONALITY")
        print("=" * 50)
        
        # Login as regular user
        if not self.login_user("john_doe", "password123"):
            print("❌ Cannot test user functionality - login failed")
            return
            
        # Test user pages
        user_pages = [
            ("/user/dashboard", "User Dashboard"),
            ("/profile", "User Profile")
        ]
        
        for url, name in user_pages:
            self.test_url(url, test_name=f"User - {name}")
            
        # Test user cannot access admin pages
        admin_pages = [
            ("/admin/dashboard", "Admin Dashboard (Should Deny)"),
            ("/admin/users", "Admin Users (Should Deny)"),
            ("/admin/reports", "Admin Reports (Should Deny)")
        ]
        
        for url, name in admin_pages:
            self.test_url(url, expected_status=302, test_name=f"User Access - {name}")
            
        self.logout()
        
    def test_form_validation(self):
        """Test form validation"""
        print("\n📝 TESTING FORM VALIDATION")
        print("=" * 50)
        
        # Test registration with missing data
        self.test_url("/register", method="POST", data={
            'username': '',
            'email': '<EMAIL>',
            'password': 'password123'
        }, test_name="Registration with Missing Username")
        
        # Test login with missing data
        self.test_url("/login", method="POST", data={
            'username': '',
            'password': ''
        }, test_name="Login with Missing Data")
        
    def test_database_operations(self):
        """Test database operations"""
        print("\n🗄️ TESTING DATABASE OPERATIONS")
        print("=" * 50)
        
        # Login as admin to test database operations
        if not self.login_user("admin", "admin123"):
            print("❌ Cannot test database operations - admin login failed")
            return
            
        # Test creating parking lot (this tests database write operations)
        create_lot_data = {
            'location_name': 'Test Parking Lot',
            'address': '123 Test Street',
            'city': 'Test City',
            'state': 'Test State',
            'pin_code': '123456',
            'price_per_hour': '50',
            'maximum_spots': '10'
        }
        
        self.test_url("/admin/create_lot", method="POST", data=create_lot_data, 
                     test_name="Create Parking Lot (Database Write)")
        
        # Test viewing users (this tests database read operations)
        self.test_url("/admin/users", test_name="View Users (Database Read)")
        
        self.logout()
        
    def run_all_tests(self):
        """Run comprehensive test suite"""
        print("🧪 COMPREHENSIVE TEST SUITE - VEHICLE PARKING MANAGEMENT SYSTEM")
        print("=" * 80)
        print(f"Testing application at: {BASE_URL}")
        print()
        
        # Run all test categories
        self.test_basic_pages()
        self.test_authentication_flow()
        self.test_forgot_password_flow()
        self.test_admin_functionality()
        self.test_user_functionality()
        self.test_form_validation()
        self.test_database_operations()
        
        # Print comprehensive summary
        self.print_summary()
        
    def print_summary(self):
        """Print comprehensive test summary"""
        print("\n📊 COMPREHENSIVE TEST SUMMARY")
        print("=" * 60)
        
        print(f"Total Tests Run: {self.total_tests}")
        print(f"✅ Tests Passed: {self.passed_tests}")
        print(f"❌ Tests Failed: {self.total_tests - self.passed_tests}")
        print(f"Success Rate: {(self.passed_tests/self.total_tests)*100:.1f}%")
        
        if self.total_tests - self.passed_tests > 0:
            print("\n❌ FAILED TESTS:")
            for result in self.test_results:
                if not result['status']:
                    print(f"  - {result['name']}: {result['details']}")
        else:
            print("\n🎉 ALL TESTS PASSED! APPLICATION IS READY FOR PRODUCTION!")
            
        print("\n🔍 TEST CATEGORIES COVERED:")
        print("  ✅ Basic Page Accessibility")
        print("  ✅ Authentication & Authorization")
        print("  ✅ Forgot Password Functionality")
        print("  ✅ Admin Features & Permissions")
        print("  ✅ User Features & Restrictions")
        print("  ✅ Form Validation")
        print("  ✅ Database Operations")
        print("  ✅ API Endpoints")
        print("  ✅ Security & Access Control")

if __name__ == "__main__":
    print("🚀 Starting Comprehensive Test Suite...")
    print("Make sure the Flask application is running at http://127.0.0.1:5000")
    print()
    
    tester = ComprehensiveTestSuite()
    tester.run_all_tests()
