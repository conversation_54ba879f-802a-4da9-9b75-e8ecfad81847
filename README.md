# Vehicle Parking Management System

A comprehensive, production-ready multi-user parking management system built with Flask. Features role-based access control, real-time parking management, advanced analytics, and a modern responsive interface.

## ✨ Features

### 🔧 Admin Features
- **📊 Comprehensive Dashboard**: Real-time overview of all parking lots, spots, and user statistics
- **🏢 Advanced Parking Lot Management**: Create, edit, and delete parking lots with JavaScript-free interface
- **📍 Location Management**: Full address support with city, state, and pin code
- **🎯 Dynamic Spot Management**: Automatically adjust parking spots based on lot capacity
- **👥 User Management**: View all registered users with detailed profiles including contact information
- **🔍 Advanced Search**: Search for parking spots by spot number or vehicle number
- **📈 Analytics & Reports**: Visual charts showing revenue trends and parking lot performance
- **🌐 RESTful API**: Complete API endpoints for data access and integration
- **🛡️ Security Controls**: Admin-only access with proper authentication

### 👤 User Features
- **🔐 Secure Authentication**: Registration and login with enhanced profile management
- **📱 Smart Booking System**: Book parking spots with date selection (up to 30 days in advance)
- **⚡ Real-time Management**: Seamless check-in and check-out functionality
- **💰 Automatic Cost Calculation**: Precise cost calculation based on duration and hourly rates
- **📊 Personal Dashboard**: Comprehensive view of booking history and statistics
- **📈 Personal Analytics**: Visual charts showing parking cost history and trends
- **🧭 Easy Navigation**: Intuitive navbar with direct links to booking and reservations sections
- **📞 Enhanced Profiles**: Complete user profiles with phone numbers and addresses

## 🛠️ Technology Stack

- **Backend**: Flask (Python web framework) with modular architecture
- **Database**: SQLite with SQLAlchemy ORM and automatic schema management
- **Frontend**: HTML5, CSS3, Bootstrap 5.3.0, Jinja2 templating
- **Charts**: Matplotlib for advanced data visualization with base64 encoding
- **API**: Flask-RESTful for comprehensive REST API endpoints
- **Validation**: Multi-layer validation (HTML5 + server-side + JavaScript-free options)
- **Timezone**: pytz for Indian Standard Time (IST) support
- **Security**: Werkzeug password hashing and session management
- **UI/UX**: Responsive design with custom gradients and modern styling
- **Testing**: Comprehensive test suites for all functionality

## 🚀 Installation & Setup

### Prerequisites
- Python 3.7 or higher
- pip (Python package installer)

### Quick Start

1. **Clone or download the project**
   ```bash
   cd Mad1_Project_Vehicle
   ```

2. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Run the application**
   ```bash
   python app.py
   ```

4. **Access the application**
   - Open your browser and go to: `http://127.0.0.1:5000`
   - The database will be automatically created on first run
   - Admin user is automatically created with sample data

### 🎯 First Time Setup
- The application automatically creates an admin user and sample data
- 6 parking lots with 180 total parking spots are pre-configured
- Sample users and reservations are created for testing

## 🔐 Default Credentials

### Admin Account
- **Username**: `admin`
- **Password**: `admin123`
- **Features**: Full system access, parking lot management, user management, analytics

### Sample User Accounts
- **john_doe** / **password123** - Regular user with sample reservations
- **jane_smith** / **password123** - Regular user with active bookings
- **mike_wilson** / **password123** - Regular user with booking history
- **alice_brown** / **password123** - Regular user for testing
- **bob_johnson** / **password123** - Regular user for testing

## 📁 Project Structure

```
Mad1_Project_Vehicle/
├── app.py                          # Main Flask application with models
├── requirements.txt                # Python dependencies
├── README.md                      # Project documentation
├── application/
│   └── controllers.py             # Route handlers and business logic (1000+ lines)
├── templates/                     # HTML templates (JavaScript-free)
│   ├── base.html                  # Base template with responsive navbar
│   ├── index.html                 # Modern home page
│   ├── login.html                 # Login form with gradient styling
│   ├── register.html              # Registration form with enhanced fields
│   ├── admin_dashboard.html       # Comprehensive admin dashboard
│   ├── admin_users.html           # User management interface
│   ├── admin_search.html          # Advanced search functionality
│   ├── admin_reports.html         # Analytics and reporting
│   ├── user_dashboard.html        # Feature-rich user dashboard
│   ├── profile.html               # User profile management
│   ├── edit_profile.html          # Profile editing interface
│   ├── edit_parking_lot.html      # Dedicated parking lot edit page
│   └── delete_parking_lot.html    # Dedicated parking lot delete page
├── static/
│   └── style.css                  # Custom CSS with modern styling
├── instance/
│   └── myproject.db              # SQLite database (auto-created)
└── test_scripts/                  # Comprehensive testing suite
    ├── test_functionality.py      # Core functionality tests
    ├── test_navbar_*.py          # Navigation testing
    └── various other test files   # Feature-specific tests
```

## 🗄️ Database Schema

### Users Table (Enhanced)
- `id` (Primary Key)
- `username` (Non-unique, allows multiple users with same name)
- `email` (Unique, primary identifier)
- `password_hash` (Werkzeug hashed)
- `phone_number` (Contact information)
- `address` (Street address)
- `city` (User's city)
- `state` (User's state)
- `pincode` (6-digit postal code)
- `is_admin` (Boolean role flag)
- `created_at` (Timestamp)

### Parking Lots Table (Enhanced)
- `id` (Primary Key)
- `location_name` (Parking lot name)
- `address` (Street address only)
- `city` (Parking lot city)
- `state` (Parking lot state)
- `pin_code` (6-digit postal code)
- `price_per_hour` (Decimal pricing)
- `maximum_spots` (Capacity)
- `created_at` (Timestamp)

### Parking Spots Table
- `id` (Primary Key)
- `lot_id` (Foreign Key to ParkingLot)
- `spot_number` (Sequential numbering)
- `status` (A=Available, O=Occupied)
- `created_at` (Timestamp)

### Reservations Table (Enhanced)
- `id` (Primary Key)
- `spot_id` (Foreign Key to ParkingSpot)
- `user_id` (Foreign Key to User)
- `vehicle_number` (License plate)
- `booking_date` (Selected booking date)
- `parking_timestamp` (Check-in time)
- `leaving_timestamp` (Check-out time)
- `parking_cost` (Calculated cost)
- `status` (booked, parked, completed)
- `created_at` (Timestamp)

## 🌐 API Endpoints

### Parking Lots API
- `GET /api/parking_lots` - Get all parking lots with availability and location details
- `GET /api/parking_lots/<id>` - Get specific parking lot with real-time spot counts

### Parking Spots API
- `GET /api/parking_spots` - Get all parking spots with status and lot information

### Reservations API
- `GET /api/reservations` - Get all reservations with user and spot details

### Chart APIs
- `GET /admin/chart` - Get admin revenue chart data (base64 encoded)
- `GET /user/chart/<user_id>` - Get user-specific parking cost chart

### Response Format
All APIs return JSON with proper error handling and include:
- Real-time data updates
- Comprehensive field coverage (including city, state, contact info)
- Fallback values for missing data
- Proper HTTP status codes

## 🔧 Key Features Implementation

### 1. 🔐 Advanced Multi-User System
- **Role-based access control** with Admin and User roles
- **Enhanced user profiles** with phone numbers and complete addresses
- **Secure password hashing** using Werkzeug
- **Session management** with proper authentication
- **Non-unique usernames** but unique email addresses for flexibility

### 2. 🏢 Smart Parking Management
- **JavaScript-free edit/delete** operations with dedicated pages
- **Automatic spot creation/deletion** based on lot capacity changes
- **Real-time availability tracking** with instant updates
- **Advanced location management** with city, state, and pin code
- **Conditional operations** (e.g., cannot delete lots with occupied spots)

### 3. 💰 Intelligent Cost Calculation
- **Time-based pricing** using Indian Standard Time (IST)
- **Automatic cost calculation** on checkout with precise timing
- **Flexible hourly rates** configurable per parking lot
- **Date-based booking** (up to 30 days in advance)

### 4. 🎨 Modern Responsive Design
- **Bootstrap 5.3.0** for cutting-edge mobile-friendly interface
- **Custom gradient styling** for forms and headers
- **Teal-green navbar** (#10b981) with professional appearance
- **Accessibility-focused** design for all users

### 5. ✅ Multi-Layer Validation
- **HTML5 client-side validation** for immediate feedback
- **Comprehensive server-side validation** with error handling
- **JavaScript-free alternatives** for better accessibility
- **Real-time feedback** for user inputs with visual indicators

### 6. 📊 Advanced Data Visualization
- **Matplotlib charts** for comprehensive admin analytics
- **Revenue tracking** with 7-day trend analysis
- **User-specific charts** showing personal parking history
- **Base64 encoded delivery** for seamless chart integration

## 📖 Usage Guide

### 🔧 For Administrators
1. **Login** with admin credentials (`admin` / `admin123`)
2. **Manage Parking Lots**: Create, edit, and delete lots using dedicated pages (no JavaScript required)
3. **Monitor Operations**: Real-time dashboard with occupancy, revenue, and user statistics
4. **User Management**: View all registered users with complete contact information
5. **Advanced Search**: Find parking spots by spot number or vehicle number
6. **Analytics**: View revenue trends and parking lot performance charts
7. **API Access**: Use RESTful endpoints for data integration

### 👤 For Users
1. **Register/Login**: Create account with complete profile (phone, address, city, state, pincode)
2. **Smart Booking**: Select parking lots and book spots up to 30 days in advance
3. **Easy Navigation**: Use navbar "Book Parking" and "My Reservations" for quick access
4. **Real-time Management**: Check-in when arriving, check-out when leaving
5. **Cost Tracking**: View automatic cost calculations and payment history
6. **Personal Analytics**: Monitor your parking patterns and expenses with visual charts
7. **Profile Management**: Update your contact information and preferences

## 🛡️ Security Features
- **Advanced password hashing** using Werkzeug security functions
- **Session-based authentication** with proper user state management
- **Role-based access control** preventing unauthorized access
- **Input sanitization and validation** at multiple layers
- **CSRF protection** through form validation and proper routing
- **Admin-only operations** with authentication checks
- **Secure database operations** with transaction management and rollback

## 🎯 Current Status & Achievements

### ✅ **Production Ready Features**
- **100% Functional** - All core features working perfectly
- **Comprehensive Testing** - Extensive test coverage with automated and manual tests
- **JavaScript-Free Options** - Full accessibility without JavaScript dependencies
- **Modern UI/UX** - Professional interface with responsive design
- **Complete CRUD Operations** - Full create, read, update, delete functionality
- **Real-time Data** - Live updates for parking availability and reservations
- **Advanced Analytics** - Visual charts and reporting for both admin and users

### 📊 **System Statistics**
- **6 Pre-configured Parking Lots** with diverse locations and pricing
- **180 Total Parking Spots** across all locations
- **Sample User Base** with realistic booking patterns
- **Complete Test Data** for immediate functionality demonstration
- **1000+ Lines of Backend Code** with comprehensive business logic
- **15+ HTML Templates** with modern responsive design

## 🧪 Testing & Quality Assurance
- **Comprehensive test suites** for all major functionality
- **Automated testing scripts** for booking, reservations, and admin operations
- **Manual testing guides** with step-by-step instructions
- **Browser compatibility testing** across different environments
- **JavaScript-free alternatives** ensuring universal accessibility
- **Performance testing** for database operations and chart generation
- **Edge case handling** with proper error messages and fallbacks

## 🚀 Recent Improvements
- ✅ **JavaScript-free edit/delete operations** for better accessibility
- ✅ **Enhanced user profiles** with complete contact information
- ✅ **Advanced booking system** with date selection capabilities
- ✅ **Fixed navbar navigation** with proper anchor links
- ✅ **Improved form styling** with modern gradients
- ✅ **Comprehensive test coverage** for all features
- ✅ **Database schema enhancements** with city/state support
- ✅ **API improvements** with complete field coverage

## 🔮 Future Enhancements
- 💳 **Payment gateway integration** for online payments
- 📧 **Email notifications** for booking confirmations
- 📱 **Mobile app development** with React Native
- 📊 **Advanced reporting features** with PDF export
- 🌍 **Multi-language support** for international users
- 🔔 **Push notifications** for booking reminders
- 🚗 **Vehicle management** with multiple vehicles per user

## 📄 License
This project is developed for educational purposes as part of the MAD1 course requirements.
The codebase demonstrates modern web development practices, comprehensive testing, and production-ready features.