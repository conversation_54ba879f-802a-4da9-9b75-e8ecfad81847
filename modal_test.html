
<!DOCTYPE html>
<html>
<head>
    <title>Modal Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h2>Modal Test</h2>
        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#testModal">
            Test Modal
        </button>
    </div>

    <!-- Test Modal -->
    <div class="modal fade" id="testModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Test Modal</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>This is a test modal to verify Bootstrap functionality.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        console.log('Bootstrap loaded');
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM loaded');
            const testButton = document.querySelector('[data-bs-target="#testModal"]');
            if (testButton) {
                console.log('Test button found');
                testButton.addEventListener('click', function() {
                    console.log('Test button clicked');
                });
            }
        });
    </script>
</body>
</html>
