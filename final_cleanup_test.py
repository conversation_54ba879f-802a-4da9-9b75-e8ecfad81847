#!/usr/bin/env python3
"""
Final Cleanup Test Script
Tests all functionality after removing redundant code and forgot password feature
"""

import requests
import json
import time
from datetime import datetime

BASE_URL = "http://127.0.0.1:5000"

def test_page(url, expected_status=200, description=""):
    """Test a page and return status"""
    try:
        response = requests.get(url, timeout=10)
        status = "✅" if response.status_code == expected_status else "❌"
        print(f"{status} {description}: {response.status_code}")
        return response.status_code == expected_status
    except Exception as e:
        print(f"❌ {description}: Error - {str(e)}")
        return False

def test_post_request(url, data, description=""):
    """Test a POST request"""
    try:
        response = requests.post(url, data=data, timeout=10, allow_redirects=False)
        status = "✅" if response.status_code in [200, 302] else "❌"
        print(f"{status} {description}: {response.status_code}")
        return response.status_code in [200, 302]
    except Exception as e:
        print(f"❌ {description}: Error - {str(e)}")
        return False

def test_api_endpoint(url, description=""):
    """Test API endpoint"""
    try:
        response = requests.get(url, timeout=10)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ {description}: {response.status_code} - Data received")
            return True
        else:
            print(f"❌ {description}: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ {description}: Error - {str(e)}")
        return False

def main():
    print("🧹 FINAL CLEANUP VERIFICATION TEST")
    print("=" * 60)
    print(f"Testing application at: {BASE_URL}")
    print(f"Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()

    # Test public pages
    print("🌐 TESTING PUBLIC PAGES")
    print("-" * 40)
    public_tests = [
        (f"{BASE_URL}/", "Home Page"),
        (f"{BASE_URL}/login", "Login Page"),
        (f"{BASE_URL}/register", "Register Page"),
    ]
    
    public_passed = 0
    for url, desc in public_tests:
        if test_page(url, 200, desc):
            public_passed += 1
    
    print()

    # Test that forgot password pages are removed
    print("🚫 VERIFYING FORGOT PASSWORD REMOVAL")
    print("-" * 40)
    forgot_tests = [
        (f"{BASE_URL}/forgot_password", "Forgot Password Page (should be 404)"),
        (f"{BASE_URL}/reset_password/test", "Reset Password Page (should be 404)"),
    ]
    
    forgot_removed = 0
    for url, desc in forgot_tests:
        if test_page(url, 404, desc):
            forgot_removed += 1
    
    print()

    # Test API endpoints
    print("🔌 TESTING API ENDPOINTS")
    print("-" * 40)
    api_tests = [
        (f"{BASE_URL}/api/parking_lots", "Parking Lots API"),
        (f"{BASE_URL}/api/parking_spots", "Parking Spots API"),
        (f"{BASE_URL}/api/reservations", "Reservations API"),
    ]
    
    api_passed = 0
    for url, desc in api_tests:
        if test_api_endpoint(url, desc):
            api_passed += 1
    
    print()

    # Test form submissions (should handle validation properly)
    print("📝 TESTING FORM VALIDATIONS")
    print("-" * 40)
    
    # Test registration form validation
    reg_data = {
        'username': '',
        'email': '',
        'password': '',
        'confirm_password': ''
    }
    form_tests = 0
    if test_post_request(f"{BASE_URL}/register", reg_data, "Registration Form Validation"):
        form_tests += 1
    
    # Test login form validation
    login_data = {
        'username': '',
        'password': ''
    }
    if test_post_request(f"{BASE_URL}/login", login_data, "Login Form Validation"):
        form_tests += 1
    
    print()

    # Test chart endpoints (should require authentication)
    print("📊 TESTING CHART ENDPOINTS")
    print("-" * 40)
    chart_tests = [
        (f"{BASE_URL}/admin/chart", "Admin Chart (should require auth)"),
        (f"{BASE_URL}/user/chart", "User Chart (should require auth)"),
    ]
    
    chart_passed = 0
    for url, desc in chart_tests:
        if test_page(url, 403, desc):  # Should return 403 Forbidden without auth
            chart_passed += 1
    
    print()

    # Summary
    print("📊 TEST SUMMARY")
    print("=" * 60)
    print(f"✅ Public Pages: {public_passed}/{len(public_tests)} passed")
    print(f"✅ Forgot Password Removed: {forgot_removed}/{len(forgot_tests)} confirmed")
    print(f"✅ API Endpoints: {api_passed}/{len(api_tests)} working")
    print(f"✅ Form Validations: {form_tests}/2 working")
    print(f"✅ Chart Security: {chart_passed}/{len(chart_tests)} secured")
    
    total_tests = len(public_tests) + len(forgot_tests) + len(api_tests) + 2 + len(chart_tests)
    total_passed = public_passed + forgot_removed + api_passed + form_tests + chart_passed
    
    print()
    print(f"🎯 OVERALL RESULT: {total_passed}/{total_tests} tests passed")
    
    if total_passed == total_tests:
        print("🎉 ALL TESTS PASSED! Application is clean and working perfectly!")
        print("✨ Code cleanup successful - redundant code removed")
        print("🚫 Forgot password functionality successfully removed")
        print("🔧 All core features working as expected")
    else:
        print("⚠️  Some tests failed. Please check the application.")
    
    print()
    print(f"Test completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
