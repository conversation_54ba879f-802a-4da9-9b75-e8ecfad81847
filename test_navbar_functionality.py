#!/usr/bin/env python3
"""
Comprehensive Navbar Functionality Test
Tests all navbar buttons, links, and dropdowns for both admin and regular users
"""

import requests
import sys
from urllib.parse import urljoin

# Base URL for the application
BASE_URL = "http://127.0.0.1:5000"

class NavbarTester:
    def __init__(self):
        self.session = requests.Session()
        self.test_results = []
        
    def log_test(self, test_name, status, details=""):
        """Log test results"""
        status_symbol = "✅" if status else "❌"
        self.test_results.append({
            'name': test_name,
            'status': status,
            'details': details
        })
        print(f"{status_symbol} {test_name}: {details}")
        
    def test_url(self, url, expected_status=200, test_name=""):
        """Test if a URL is accessible"""
        try:
            response = self.session.get(urljoin(BASE_URL, url))
            success = response.status_code == expected_status
            details = f"Status: {response.status_code}"
            if not success and response.status_code == 302:
                details += f" (Redirect to: {response.headers.get('Location', 'Unknown')})"
            self.log_test(test_name or f"URL: {url}", success, details)
            return success, response
        except Exception as e:
            self.log_test(test_name or f"URL: {url}", False, f"Error: {str(e)}")
            return False, None
            
    def login_user(self, username, password):
        """Login with given credentials"""
        login_data = {
            'username': username,
            'password': password
        }
        try:
            response = self.session.post(urljoin(BASE_URL, '/login'), data=login_data)
            success = response.status_code in [200, 302]
            self.log_test(f"Login as {username}", success, f"Status: {response.status_code}")
            return success
        except Exception as e:
            self.log_test(f"Login as {username}", False, f"Error: {str(e)}")
            return False
            
    def logout(self):
        """Logout current user"""
        try:
            response = self.session.get(urljoin(BASE_URL, '/logout'))
            success = response.status_code in [200, 302]
            self.log_test("Logout", success, f"Status: {response.status_code}")
            return success
        except Exception as e:
            self.log_test("Logout", False, f"Error: {str(e)}")
            return False
            
    def test_admin_navbar(self):
        """Test all admin navbar functionality"""
        print("\n🔧 TESTING ADMIN NAVBAR FUNCTIONALITY")
        print("=" * 50)
        
        # Login as admin
        if not self.login_user("admin", "admin123"):
            print("❌ Cannot test admin navbar - login failed")
            return
            
        # Test admin navbar links
        admin_links = [
            ("/admin/dashboard", "Admin Dashboard"),
            ("/admin/users", "Users Page"),
            ("/admin/search", "Search Spots"),
            ("/admin/reports", "Reports Page"),
            ("/profile", "View/Edit Profile")
        ]
        
        for url, name in admin_links:
            self.test_url(url, test_name=f"Admin - {name}")
            
        # Test dropdown functionality (these should be accessible)
        dropdown_links = [
            ("/admin/dashboard", "Manage - Parking Lots"),
            ("/admin/reports", "Manage - Reports")
        ]
        
        for url, name in dropdown_links:
            self.test_url(url, test_name=f"Admin Dropdown - {name}")
            
        self.logout()
        
    def test_user_navbar(self):
        """Test all user navbar functionality"""
        print("\n👤 TESTING USER NAVBAR FUNCTIONALITY")
        print("=" * 50)
        
        # Login as regular user
        if not self.login_user("john_doe", "password123"):
            print("❌ Cannot test user navbar - login failed")
            return
            
        # Test user navbar links
        user_links = [
            ("/user/dashboard", "User Dashboard"),
            ("/profile", "View/Edit Profile")
        ]
        
        for url, name in user_links:
            self.test_url(url, test_name=f"User - {name}")
            
        # Test user dashboard sections (these are anchor links)
        dashboard_sections = [
            ("/user/dashboard#book-parking", "Book Parking Section"),
            ("/user/dashboard#my-reservations", "My Reservations Section")
        ]
        
        for url, name in dashboard_sections:
            # For anchor links, just test the base page
            base_url = url.split('#')[0]
            self.test_url(base_url, test_name=f"User - {name}")
            
        self.logout()
        
    def test_common_functionality(self):
        """Test functionality common to both admin and user"""
        print("\n🔄 TESTING COMMON FUNCTIONALITY")
        print("=" * 50)
        
        # Test login page accessibility
        self.test_url("/login", test_name="Login Page")
        
        # Test register page accessibility
        self.test_url("/register", test_name="Register Page")
        
        # Test home page
        self.test_url("/", test_name="Home Page")
        
    def test_protected_routes(self):
        """Test that protected routes redirect when not logged in"""
        print("\n🔒 TESTING PROTECTED ROUTES (Should redirect)")
        print("=" * 50)
        
        # Ensure we're logged out
        self.logout()
        
        protected_routes = [
            ("/admin/dashboard", "Admin Dashboard (Protected)"),
            ("/admin/users", "Admin Users (Protected)"),
            ("/admin/search", "Admin Search (Protected)"),
            ("/admin/reports", "Admin Reports (Protected)"),
            ("/user/dashboard", "User Dashboard (Protected)"),
            ("/profile", "Profile (Protected)")
        ]
        
        for url, name in protected_routes:
            # These should redirect (302) to login page
            self.test_url(url, expected_status=302, test_name=f"Protection - {name}")
            
    def run_all_tests(self):
        """Run comprehensive navbar functionality tests"""
        print("🧪 COMPREHENSIVE NAVBAR FUNCTIONALITY TEST")
        print("=" * 60)
        print(f"Testing application at: {BASE_URL}")
        print()
        
        # Test basic accessibility
        self.test_common_functionality()
        
        # Test protected routes
        self.test_protected_routes()
        
        # Test admin functionality
        self.test_admin_navbar()
        
        # Test user functionality
        self.test_user_navbar()
        
        # Print summary
        self.print_summary()
        
    def print_summary(self):
        """Print test summary"""
        print("\n📊 TEST SUMMARY")
        print("=" * 50)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result['status'])
        failed_tests = total_tests - passed_tests
        
        print(f"Total Tests: {total_tests}")
        print(f"✅ Passed: {passed_tests}")
        print(f"❌ Failed: {failed_tests}")
        print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        
        if failed_tests > 0:
            print("\n❌ FAILED TESTS:")
            for result in self.test_results:
                if not result['status']:
                    print(f"  - {result['name']}: {result['details']}")
        else:
            print("\n🎉 ALL TESTS PASSED!")

if __name__ == "__main__":
    print("Starting navbar functionality tests...")
    print("Make sure the Flask application is running at http://127.0.0.1:5000")
    print()
    
    tester = NavbarTester()
    tester.run_all_tests()
