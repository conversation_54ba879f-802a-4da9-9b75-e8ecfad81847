#!/usr/bin/env python3
"""
Test Edit Modal UI
Tests that the edit modal form is properly populated with current values
"""

import requests
from datetime import datetime

BASE_URL = "http://127.0.0.1:5000"

def test_edit_modal_ui():
    """Test that edit modal shows current parking lot values"""
    print("🖥️ TESTING EDIT MODAL UI")
    print("=" * 50)
    print(f"Testing at: {BASE_URL}")
    print(f"Test started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()

    # Create a session to maintain cookies
    session = requests.Session()

    # Step 1: Admin login
    print("🔐 Step 1: Admin Login")
    print("-" * 30)
    
    login_data = {
        'username': 'admin',
        'password': 'admin123'
    }
    
    try:
        login_response = session.post(f"{BASE_URL}/login", data=login_data, allow_redirects=False)
        if login_response.status_code == 302:
            print("✅ Admin login successful")
        else:
            print(f"❌ Admin login failed: {login_response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Login error: {str(e)}")
        return False

    print()

    # Step 2: Get admin dashboard HTML
    print("🏠 Step 2: Get Admin Dashboard HTML")
    print("-" * 30)
    
    try:
        dashboard_response = session.get(f"{BASE_URL}/admin/dashboard")
        if dashboard_response.status_code == 200:
            html_content = dashboard_response.text
            print("✅ Admin dashboard loaded successfully")
            
            # Check if edit modals are present
            if "editModal" in html_content:
                print("✅ Edit modals found in HTML")
            else:
                print("❌ Edit modals NOT found in HTML")
                return False
                
            # Check if city and state fields are in edit modals
            if 'name="city"' in html_content and 'name="state"' in html_content:
                print("✅ City and state fields found in edit forms")
            else:
                print("❌ City and state fields NOT found in edit forms")
                return False
                
            # Check for proper form structure
            if 'action="{{ url_for(\'edit_parking_lot\', lot_id=lot.id) }}"' in html_content:
                print("✅ Edit form action URLs properly configured")
            else:
                print("❌ Edit form action URLs not properly configured")
                return False
                
            # Check for Bootstrap modal structure
            if 'class="modal fade"' in html_content and 'data-bs-toggle="modal"' in html_content:
                print("✅ Bootstrap modal structure present")
            else:
                print("❌ Bootstrap modal structure missing")
                return False
                
            print("✅ Edit modal UI structure is correct")
            return True
            
        else:
            print(f"❌ Dashboard access failed: {dashboard_response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Dashboard error: {str(e)}")
        return False

def test_form_field_population():
    """Test that form fields are populated with current values"""
    print("\n📝 TESTING FORM FIELD POPULATION")
    print("=" * 50)
    
    session = requests.Session()
    
    # Login as admin
    login_data = {'username': 'admin', 'password': 'admin123'}
    session.post(f"{BASE_URL}/login", data=login_data)
    
    try:
        # Get dashboard HTML
        dashboard_response = session.get(f"{BASE_URL}/admin/dashboard")
        html_content = dashboard_response.text
        
        # Get parking lots from API to compare
        api_response = session.get(f"{BASE_URL}/api/parking_lots")
        if api_response.status_code == 200:
            data = api_response.json()
            parking_lots = data.get('parking_lots', [])
            
            if parking_lots:
                # Check first parking lot
                lot = parking_lots[0]
                lot_id = lot['id']
                
                print(f"Checking form population for lot ID {lot_id}:")
                print(f"  Name: {lot['location_name']}")
                print(f"  City: {lot['city']}")
                print(f"  State: {lot['state']}")
                print(f"  Price: {lot['price_per_hour']}")
                
                # Check if values appear in the HTML
                checks_passed = 0
                total_checks = 4
                
                if f'value="{lot["location_name"]}"' in html_content:
                    print("  ✅ Location name populated in form")
                    checks_passed += 1
                else:
                    print("  ❌ Location name NOT populated in form")
                
                if f'value="{lot["city"]}"' in html_content:
                    print("  ✅ City populated in form")
                    checks_passed += 1
                else:
                    print("  ❌ City NOT populated in form")
                
                if f'value="{lot["state"]}"' in html_content:
                    print("  ✅ State populated in form")
                    checks_passed += 1
                else:
                    print("  ❌ State NOT populated in form")
                
                if f'value="{lot["price_per_hour"]}"' in html_content:
                    print("  ✅ Price populated in form")
                    checks_passed += 1
                else:
                    print("  ❌ Price NOT populated in form")
                
                if checks_passed == total_checks:
                    print("✅ All form fields properly populated")
                    return True
                else:
                    print(f"⚠️ {checks_passed}/{total_checks} form fields populated")
                    return False
            else:
                print("❌ No parking lots found to test")
                return False
        else:
            print(f"❌ API request failed: {api_response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Form population test error: {str(e)}")
        return False

def main():
    print("🖥️ EDIT MODAL UI TEST SUITE")
    print("=" * 60)
    
    # Test 1: UI structure
    ui_success = test_edit_modal_ui()
    
    # Test 2: Form field population
    form_success = test_form_field_population()
    
    print("\n📊 UI TEST SUMMARY")
    print("=" * 60)
    if ui_success and form_success:
        print("🎉 EDIT MODAL UI WORKING PERFECTLY!")
        print("✅ Modal structure correct")
        print("✅ Form fields present")
        print("✅ Form actions configured")
        print("✅ Bootstrap modals working")
        print("✅ Form fields populated with current values")
        print("\n🔧 The edit parking lot UI is fully functional!")
    else:
        print("❌ EDIT MODAL UI HAS ISSUES")
        if not ui_success:
            print("❌ UI structure problems")
        if not form_success:
            print("❌ Form population problems")
    
    print(f"\nTest completed: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
