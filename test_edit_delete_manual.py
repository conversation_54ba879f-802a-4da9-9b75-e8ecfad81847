#!/usr/bin/env python3
"""
Manual test for Edit and Delete Parking Lot functionality
"""

import requests
import webbrowser
import time

BASE_URL = "http://127.0.0.1:5000"

def test_admin_dashboard_ui():
    """Test admin dashboard UI elements"""
    print("🖥️ TESTING ADMIN DASHBOARD UI")
    print("=" * 40)
    
    session = requests.Session()
    
    # Login as admin
    login_data = {'username': 'admin', 'password': 'admin123'}
    login_response = session.post(f"{BASE_URL}/login", data=login_data)
    
    if login_response.status_code == 200 or login_response.status_code == 302:
        print("✅ Admin login successful")
        
        # Get admin dashboard
        dashboard_response = session.get(f"{BASE_URL}/admin/dashboard")
        html_content = dashboard_response.text
        
        # Check UI elements
        edit_buttons = html_content.count('data-bs-target="#editModal')
        delete_buttons = html_content.count('data-bs-target="#deleteModal')
        
        print(f"✅ Found {edit_buttons} edit buttons")
        print(f"✅ Found {delete_buttons} delete buttons")
        
        # Check for modal structures
        edit_modals = html_content.count('id="editModal')
        delete_modals = html_content.count('id="deleteModal')
        
        print(f"✅ Found {edit_modals} edit modals")
        print(f"✅ Found {delete_modals} delete modals")
        
        # Check for form fields in edit modals
        if 'name="location_name"' in html_content:
            print("✅ Location name field found in edit forms")
        if 'name="address"' in html_content:
            print("✅ Address field found in edit forms")
        if 'name="city"' in html_content:
            print("✅ City field found in edit forms")
        if 'name="state"' in html_content:
            print("✅ State field found in edit forms")
        if 'name="price_per_hour"' in html_content:
            print("✅ Price field found in edit forms")
        if 'name="maximum_spots"' in html_content:
            print("✅ Maximum spots field found in edit forms")
        
        # Check for modal backdrop fix
        if 'modal backdrop fix' in html_content.lower():
            print("✅ Modal backdrop fix is present")
        
        # Check for Bootstrap
        if 'bootstrap@5.3.0' in html_content:
            print("✅ Bootstrap 5.3.0 is loaded")
        
        return True, edit_buttons, delete_buttons
    else:
        print("❌ Admin login failed")
        return False, 0, 0

def test_parking_lot_data():
    """Test parking lot data availability"""
    print("\n📊 TESTING PARKING LOT DATA")
    print("=" * 40)
    
    session = requests.Session()
    
    # Login as admin
    login_data = {'username': 'admin', 'password': 'admin123'}
    session.post(f"{BASE_URL}/login", data=login_data)
    
    # Get admin dashboard and extract parking lot data
    dashboard_response = session.get(f"{BASE_URL}/admin/dashboard")
    html_content = dashboard_response.text
    
    # Look for parking lot table data
    if '<tbody>' in html_content and '</tbody>' in html_content:
        tbody_start = html_content.find('<tbody>')
        tbody_end = html_content.find('</tbody>')
        tbody_content = html_content[tbody_start:tbody_end]
        
        # Count table rows (excluding header)
        row_count = tbody_content.count('<tr>')
        print(f"✅ Found {row_count} parking lots in table")
        
        # Extract some parking lot names for testing
        import re
        lot_names = re.findall(r'<td><strong>(.*?)</strong></td>', tbody_content)
        
        if lot_names:
            print("📋 Available parking lots:")
            for i, name in enumerate(lot_names[:5], 1):  # Show first 5
                print(f"   {i}. {name}")
            return True, lot_names
        else:
            print("❌ No parking lot names found")
            return False, []
    else:
        print("❌ No parking lot table found")
        return False, []

def create_manual_test_instructions():
    """Create detailed manual testing instructions"""
    print("\n📋 MANUAL TESTING INSTRUCTIONS")
    print("=" * 50)
    
    instructions = """
🔍 MANUAL EDIT/DELETE TESTING GUIDE:

STEP 1: OPEN ADMIN DASHBOARD
✅ Go to: http://127.0.0.1:5000/admin/dashboard
✅ Login with: admin / admin123
✅ Scroll down to "Manage Parking Lots" section

STEP 2: TEST EDIT FUNCTIONALITY
✅ Click any "Edit" button (blue button with pencil icon)
✅ Verify edit modal opens without backdrop issues
✅ Check all form fields are populated with current data:
   - Location Name
   - Address  
   - City
   - State
   - Pin Code
   - Price per Hour
   - Maximum Spots
✅ Modify some fields (e.g., change location name)
✅ Click "Update Parking Lot" button
✅ Verify modal closes properly (no dark overlay remains)
✅ Check if changes are reflected in the table

STEP 3: TEST DELETE FUNCTIONALITY
✅ Click any "Delete" button (red button with trash icon)
✅ Verify delete confirmation modal opens
✅ Read the warning message
✅ If lot has occupied spots, delete should fail
✅ If lot is empty, you can proceed with deletion
✅ Click "Delete" to confirm or "Cancel" to abort
✅ Verify modal closes properly
✅ If deleted, verify lot is removed from table

STEP 4: TEST MODAL BACKDROP FIX
✅ Open any modal (edit or delete)
✅ Close modal using different methods:
   - Click X button (top right)
   - Click outside modal (on dark area)
   - Press Escape key
✅ Verify dark backdrop disappears completely
✅ Verify page is usable after closing modal

STEP 5: BROWSER CONSOLE TESTING
✅ Open Developer Tools (F12)
✅ Go to Console tab
✅ Look for these messages when opening/closing modals:
   - "Modal backdrop fix initialized for X modals"
   - "Modal shown event triggered for: editModalX"
   - "Modal hidden event triggered for: editModalX"
   - "Modal backdrop cleaned up"

EXPECTED RESULTS:
✅ Edit modals open and close smoothly
✅ All form fields work correctly
✅ Changes are saved and reflected immediately
✅ Delete modals work for empty parking lots
✅ No dark overlay remains after closing modals
✅ Console shows proper event messages

TROUBLESHOOTING:
❌ If modal doesn't open: Check console for JavaScript errors
❌ If backdrop stays dark: Run cleanupStuckBackdrops() in console
❌ If edit doesn't save: Check form validation and required fields
❌ If delete fails: Ensure parking lot has no occupied spots
"""
    
    print(instructions)
    
    # Save to file
    with open('manual_edit_delete_test.txt', 'w', encoding='utf-8') as f:
        f.write(instructions)
    
    print("📄 Instructions saved to: manual_edit_delete_test.txt")

def main():
    print("🧪 EDIT/DELETE PARKING LOT FUNCTIONALITY TEST")
    print("=" * 60)
    
    # Test UI elements
    ui_success, edit_count, delete_count = test_admin_dashboard_ui()
    
    # Test data availability
    data_success, lot_names = test_parking_lot_data()
    
    # Create manual test instructions
    create_manual_test_instructions()
    
    print("\n📊 AUTOMATED TEST RESULTS")
    print("=" * 40)
    print(f"✅ Admin Dashboard Access: {'PASS' if ui_success else 'FAIL'}")
    print(f"✅ Edit Buttons Found: {edit_count}")
    print(f"✅ Delete Buttons Found: {delete_count}")
    print(f"✅ Parking Lot Data: {'PASS' if data_success else 'FAIL'}")
    print(f"✅ Available Lots: {len(lot_names) if lot_names else 0}")
    
    if ui_success and data_success:
        print("\n🎯 AUTOMATED TESTS: ✅ PASSED")
        print("🔧 UI elements are properly configured")
        print("📊 Parking lot data is available")
        print("🎨 Modal backdrop fix is in place")
        
        print("\n🌐 Opening admin dashboard for manual testing...")
        webbrowser.open(f"{BASE_URL}/admin/dashboard")
        
        print("\n💡 NEXT STEPS:")
        print("1. Follow the manual testing instructions above")
        print("2. Test edit functionality with real user interaction")
        print("3. Test delete functionality (be careful with data)")
        print("4. Verify modal backdrop fix works in browser")
        print("5. Check browser console for any JavaScript errors")
        
    else:
        print("\n❌ AUTOMATED TESTS: FAILED")
        print("🔍 Check application setup and data availability")

if __name__ == "__main__":
    main()
