#!/usr/bin/env python3
"""
Quick test to verify booking date functionality
"""

import requests
from datetime import date

BASE_URL = "http://127.0.0.1:5000"

def test_user_dashboard():
    """Test user dashboard with different users"""
    print("🧪 TESTING USER DASHBOARD ACCESS")
    print("=" * 50)
    
    # Try different user credentials
    users_to_try = [
        {'username': 'john_doe', 'password': 'password123'},
        {'username': 'jane_smith', 'password': 'password123'},
        {'username': 'mike_wilson', 'password': 'password123'},
        {'username': 'sarah_davis', 'password': 'password123'},
        {'username': 'alex_brown', 'password': 'password123'},
    ]
    
    session = requests.Session()
    
    for user in users_to_try:
        print(f"Trying user: {user['username']}")
        login_response = session.post(f"{BASE_URL}/login", data=user, allow_redirects=False)
        
        if login_response.status_code == 302:
            print(f"✅ Login successful for {user['username']}")
            
            # Get user dashboard
            dashboard_response = session.get(f"{BASE_URL}/user/dashboard")
            if dashboard_response.status_code == 200:
                print("✅ User dashboard accessible")
                
                # Check for date input field
                if 'type="date"' in dashboard_response.text:
                    print("✅ Date input field found!")
                    
                    # Check for booking_date field
                    if 'name="booking_date"' in dashboard_response.text:
                        print("✅ Booking date field properly named")
                    
                    # Check for JavaScript date restrictions
                    if 'booking_date' in dashboard_response.text and 'min =' in dashboard_response.text:
                        print("✅ Date restriction JavaScript found")
                    
                    return True
                else:
                    print("❌ Date input field not found")
            else:
                print("❌ Cannot access user dashboard")
            
            # Logout
            session.get(f"{BASE_URL}/logout")
            break
        else:
            print(f"❌ Login failed for {user['username']}")
    
    return False

def main():
    print("🔍 QUICK BOOKING DATE TEST")
    print("=" * 50)
    
    result = test_user_dashboard()
    
    if result:
        print("\n✅ BOOKING DATE FEATURE SUCCESSFULLY IMPLEMENTED!")
        print("📅 Users can now select booking dates")
        print("🔧 Date validation is in place")
        print("💡 Manual testing recommended to verify full functionality")
    else:
        print("\n❌ BOOKING DATE FEATURE NEEDS INVESTIGATION")
        print("🔍 Check user credentials or template rendering")

if __name__ == "__main__":
    main()
